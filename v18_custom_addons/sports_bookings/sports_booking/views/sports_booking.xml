<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_sports_booking_form" model="ir.ui.view">
        <field name="name">sports.booking.form</field>
        <field name="model">sports.booking</field>
        <field name="arch" type="xml">
            <form string="Sports Booking">
                <header>
                    <button name="action_confirm" string="Confirm" type="object"
                            class="btn btn-primary"
                            invisible="state != 'draft'"/>

                    <button name="action_cancel" string="Cancel" type="object"
                            class="btn btn-danger"
                            invisible="state not in ['confirmed', 'in_progress']"/>

                    <button name="action_done" string="Mark as Done" type="object"
                            class="btn btn-success"
                            invisible="state != 'confirmed'"/>

                    <button name="action_send_mail" string="Send Email" type="object"
                            class="btn btn-secondary"
                            invisible="state != 'confirmed'"/>

                    <button name="action_set_draft" string="Mark as Draft" type="object"
                            class="btn btn-warning"
                            invisible="state not in ['cancelled', 'done']"/>

                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,confirmed,in_progress,done,cancelled"/>
                </header>
                <header>
                    <button name="%(action_feedback_ratings)d" type="action"
                            string="Feedback Ratings"
                            icon="fa-star"
                            class="oe_stat_button"
                            modifiers="{'readonly': [('feedback_count','=', 0)]}">
                        <field name="feedback_count" widget="statinfo"/>
                    </button>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="user_id"/>
                            <field name="players_count" readonly="is_team_booking" force_save="1"/>
                            <field name="language_id"/>
                        </group>
                        <group>
                            <field name="is_team_booking"/>
                            <field name="team_id" readonly="not is_team_booking" required="is_team_booking"/>
                            <field name="ground_id"/>
                            <field name="game_name"/>
                        </group>
                        <group>
                            <field name="price_per_hour"/>
<!--                            <field name="extra_players"/>-->
                            <!--                            <field name="extra_charge_per_player"/>-->
                        </group>
                        <group>
                            <field name="booking_date"/>
                            <field name="start_datetime" readonly="1" force_save="1"/>
                            <field name="stop_datetime" readonly="1" force_save="1"/>

                            <field name="slot_id"
                                   domain="[('ground_id', '=', ground_id), ('date', '=', booking_date), ('is_booked', '=', False)]"/>
                        </group>
                    </group>
                    <group string="Payment Info" col="4">
                        <field name="total_price" readonly="1"/>
                        <field name="discount"/>
                        <field name="duration_hours" readonly="1"/>
                        <field name="extra_player_charge"/>
                        <field name="final_price" readonly="1"/>
                        <field name="payment_method"/>
                        <field name="payment_status"/>
                    </group>
                    <notebook>
                        <page>
                            <field name="player_ids" modifiers="{'readonly': [('is_team_booking', '=', True)]}"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_sports_booking_tree" model="ir.ui.view">
        <field name="name">sports.booking.tree</field>
        <field name="model">sports.booking</field>
        <field name="arch" type="xml">
            <list string="Bookings">
                <field name="name"/>
                <field name="ground_id"/>
                <field name="start_datetime"/>
                <field name="stop_datetime"/>
                <field name="state"/>
                <field name="sport_rate"/>
                <field name="final_price"/>
                <field name="payment_status"/>
            </list>
        </field>
    </record>
</odoo>
