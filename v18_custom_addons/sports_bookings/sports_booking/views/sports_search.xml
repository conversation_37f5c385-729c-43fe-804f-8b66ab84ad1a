<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="search_results_template" name="Search Results">
        <t t-call="website.layout">
            <div class="container mt-5">
                <h2>Search Results</h2>
                <p t-if="search_location or search_date">
                    Showing results for:
                    <t t-if="search_location">city:
                        <strong>
                            <t t-esc="search_location"/>
                        </strong>
                    </t>
                    <t t-if="search_date">| Date:
                        <strong>
                            <t t-esc="search_date"/>
                        </strong>
                    </t>
                </p>

                <t t-if="grounds">
                    <div class="row">
                        <t t-foreach="grounds" t-as="ground">
                            <div class="col-md-4 mb-4">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h4>
                                            <t t-esc="ground.name"/>
                                        </h4>
                                        <p>
                                            <strong>city:</strong>
                                            <t t-esc="ground.city"/>
                                        </p>
                                        <a t-attf-href="/book/{{ ground.id }}" class="btn btn-primary btn-sm">
                                            View Slots
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </t>
                <t t-else="">
                    <p class="mt-4">No grounds found matching your criteria.</p>
                </t>
            </div>
        </t>
    </template>

    <!-- Search Results Action -->
    <record id="action_search_results" model="ir.actions.act_url">
        <field name="name">Search Results</field>
        <field name="url">/search_results</field>
        <field name="target">self</field>
    </record>

    <menuitem id="menu_search_results"
              name="Search Results"
              action="sports_booking.action_search_results"
              parent="menu_sports_root"
              sequence="40"/>
</odoo>
