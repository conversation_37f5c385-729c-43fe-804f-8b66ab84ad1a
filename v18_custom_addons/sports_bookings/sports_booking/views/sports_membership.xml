<odoo>
    <!-- Form View -->
    <record id="view_sports_membership_form" model="ir.ui.view">
        <field name="name">sports.membership.form</field>
        <field name="model">sports.membership</field>
        <field name="arch" type="xml">
            <form string="Membership">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="sport_type"/>
                        <field name="validity_days"/>
                        <field name="slot_limit"/>
                        <field name="price"/>
                        <field name="user_id"/>
                        <field name="ground_access" widget="many2many_tags"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_sports_membership_tree" model="ir.ui.view">
        <field name="name">sports.membership.tree</field>
        <field name="model">sports.membership</field>
        <field name="arch" type="xml">
            <list string="Memberships">
                <field name="name"/>
                <field name="sport_type"/>
                <field name="validity_days"/>
                <field name="price"/>
                <field name="user_id"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_sports_membership" model="ir.actions.act_window">
        <field name="name">Memberships</field>
        <field name="res_model">sports.membership</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_sports_membership"
              name="Memberships"
              parent="menu_sports_booking_configuration"
              action="action_sports_membership"
              sequence="40"/>
</odoo>
