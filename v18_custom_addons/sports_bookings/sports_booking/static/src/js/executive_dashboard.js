///** @odoo-module **/
//
//import { Component, onWillStart, useState, onMounted, useRef } from '@odoo/owl';
//import { registry } from '@web/core/registry';
//import { rpc } from "@web/core/network/rpc";
//import { loadJS } from "@web/core/assets";
//
//class ExecutiveDashboard extends Component {
//    static template = 'executive_dashboard.Template';
//
//    setup() {
//        this.state = useState({ data: null, loading: true });
//        this.sportChartRef = useRef("sportChart");
//        this.trendChartRef = useRef("trendChart");
//        this.topDaysChartRef = useRef("topDaysChart");
//        this.topGroundsChartRef = useRef("topGroundsChart");
//        this.monthlyRevenueChartRef = useRef("monthlyRevenueChart");
//        this.sportChart = null;
//        this.trendChart = null;
//        this.topDaysChart = null;
//        this.topGroundsChart = null;
//        this.monthlyRevenueChart = null;
//
//        onWillStart(async () => {
//            await loadJS('/web/static/lib/Chart/Chart.js');
//            const result = await rpc('/executive_dashboard/data', {});
//            this.state.data = result;
//            this.state.loading = false;
//        });
//
//        onMounted(() => {
//            if (this.state.data) {
//                this.renderCharts();
//            }
//        });
//    }
//
//    renderCharts() {
//        if (!this.state.data) return;
//
//        const { sports_data, trend_data, top_booking_days, top_grounds_data, monthly_revenue } = this.state.data;
//
//        if (this.sportChart) this.sportChart.destroy();
//        if (this.trendChart) this.trendChart.destroy();
//        if (this.topDaysChart) this.topDaysChart.destroy();
//        if (this.topGroundsChart) this.topGroundsChart.destroy();
//        if (this.monthlyRevenueChart) this.monthlyRevenueChart.destroy();
//
//        const sportCtx = this.sportChartRef.el;
//        if (sportCtx && Object.keys(sports_data).length > 0) {
//            this.sportChart = new Chart(sportCtx, {
//                type: 'pie',
//                data: {
//                    labels: Object.keys(sports_data),
//                    datasets: [{
//                        data: Object.values(sports_data),
//                        backgroundColor: [
//                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
//                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
//                        ],
//                        borderWidth: 2,
//                        borderColor: '#fff'
//                    }]
//                },
//                options: {
//                    responsive: true,
//                    maintainAspectRatio: false,
//                    plugins: {
//                        legend: {
//                            position: 'bottom',
//                            labels: {
//                                padding: 20,
//                                usePointStyle: true
//                            }
//                        },
//                        title: {
//                            display: true,
//                            text: 'Sports Distribution Today'
//                        }
//                    }
//                }
//            });
//        }
//
//        const trendCtx = this.trendChartRef.el;
//        if (trendCtx && trend_data.length > 0) {
//            this.trendChart = new Chart(trendCtx, {
//                type: 'line',
//                data: {
//                    labels: trend_data.map(d => d.day),
//                    datasets: [{
//                        label: 'Bookings per Day',
//                        data: trend_data.map(d => d.count),
//                        borderColor: '#36A2EB',
//                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
//                        fill: true,
//                        tension: 0.4,
//                        pointBackgroundColor: '#36A2EB',
//                        pointBorderColor: '#fff',
//                        pointBorderWidth: 2,
//                        pointRadius: 5
//                    }]
//                },
//                options: {
//                    responsive: true,
//                    maintainAspectRatio: false,
//                    scales: {
//                        y: {
//                            beginAtZero: true,
//                            ticks: {
//                                stepSize: 1
//                            },
//                            grid: {
//                                color: 'rgba(0,0,0,0.1)'
//                            }
//                        },
//                        x: {
//                            grid: {
//                                display: false
//                            }
//                        }
//                    },
//                    plugins: {
//                        legend: {
//                            display: true,
//                            position: 'top'
//                        },
//                        title: {
//                            display: true,
//                            text: 'Booking Trend (Last 7 Days)'
//                        }
//                    }
//                }
//            });
//        }
//
//        const topDaysCtx = this.topDaysChartRef.el;
//        if (topDaysCtx && top_booking_days.length > 0) {
//            this.topDaysChart = new Chart(topDaysCtx, {
//                type: 'bar',
//                data: {
//                    labels: top_booking_days.map(d => d.date),
//                    datasets: [{
//                        label: 'Number of Bookings',
//                        data: top_booking_days.map(d => d.count),
//                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
//                        borderColor: 'rgba(75, 192, 192, 1)',
//                        borderWidth: 1,
//                        borderRadius: 4,
//                        borderSkipped: false,
//                    }]
//                },
//                options: {
//                    responsive: true,
//                    maintainAspectRatio: false,
//                    scales: {
//                        y: {
//                            beginAtZero: true,
//                            ticks: {
//                                stepSize: 1
//                            },
//                            grid: {
//                                color: 'rgba(0,0,0,0.1)'
//                            }
//                        },
//                        x: {
//                            grid: {
//                                display: false
//                            }
//                        }
//                    },
//                    plugins: {
//                        legend: {
//                            display: true,
//                            position: 'top'
//                        },
//                        title: {
//                            display: true,
//                            text: 'Top 7 High Booking Days (Last 30 Days)'
//                        }
//                    }
//                }
//            });
//        }
//
//        const topGroundsCtx = this.topGroundsChartRef.el;
//        if (topGroundsCtx && top_grounds_data.length > 0) {
//            this.topGroundsChart = new Chart(topGroundsCtx, {
//                type: 'bar',
//                data: {
//                    labels: top_grounds_data.map(d => d.ground),
//                    datasets: [{
//                        label: 'Number of Bookings',
//                        data: top_grounds_data.map(d => d.bookings),
//                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
//                        borderColor: 'rgba(255, 99, 132, 1)',
//                        borderWidth: 1,
//                        borderRadius: 4,
//                        borderSkipped: false,
//                    }]
//                },
//                options: {
//                    responsive: true,
//                    maintainAspectRatio: false,
//                    indexAxis: 'y',
//                    scales: {
//                        x: {
//                            beginAtZero: true,
//                            ticks: {
//                                stepSize: 1
//                            },
//                            grid: {
//                                color: 'rgba(0,0,0,0.1)'
//                            }
//                        },
//                        y: {
//                            grid: {
//                                display: false
//                            }
//                        }
//                    },
//                    plugins: {
//                        legend: {
//                            display: true,
//                            position: 'top'
//                        },
//                        title: {
//                            display: true,
//                            text: 'Top Booking Grounds (Last 30 Days)'
//                        }
//                    }
//                }
//            });
//        }
//
//        const monthlyRevenueCtx = this.monthlyRevenueChartRef.el;
//        if (monthlyRevenueCtx && monthly_revenue.length > 0) {
//            this.monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
//                type: 'bar',
//                data: {
//                    labels: monthly_revenue.map(d => d.month),
//                    datasets: [{
//                        label: 'Revenue ($)',
//                        data: monthly_revenue.map(d => d.revenue),
//                        backgroundColor: 'rgba(153, 102, 255, 0.6)',
//                        borderColor: 'rgba(153, 102, 255, 1)',
//                        borderWidth: 1,
//                        borderRadius: 4,
//                        borderSkipped: false,
//                    }]
//                },
//                options: {
//                    responsive: true,
//                    maintainAspectRatio: false,
//                    scales: {
//                        y: {
//                            beginAtZero: true,
//                            ticks: {
//                                callback: function(value) {
//                                    return '$' + value.toLocaleString();
//                                }
//                            },
//                            grid: {
//                                color: 'rgba(0,0,0,0.1)'
//                            }
//                        },
//                        x: {
//                            grid: {
//                                display: false
//                            }
//                        }
//                    },
//                    plugins: {
//                        legend: {
//                            display: true,
//                            position: 'top'
//                        },
//                        title: {
//                            display: true,
//                            text: 'Monthly Revenue Comparison (Last 6 Months)'
//                        },
//                        tooltip: {
//                            callbacks: {
//                                label: function(context) {
//                                    return 'Revenue: $' + context.parsed.y.toLocaleString();
//                                }
//                            }
//                        }
//                    }
//                }
//            });
//        }
//    }
//
//    formatCurrency(amount) {
//        return new Intl.NumberFormat('en-US', {
//            style: 'currency',
//            currency: 'USD'
//        }).format(amount);
//    }
//
//    willUnmount() {
//        if (this.sportChart) this.sportChart.destroy();
//        if (this.trendChart) this.trendChart.destroy();
//        if (this.topDaysChart) this.topDaysChart.destroy();
//        if (this.topGroundsChart) this.topGroundsChart.destroy();
//        if (this.monthlyRevenueChart) this.monthlyRevenueChart.destroy();
//    }
//}
//
//registry.category("actions").add("executive_dashboard_tag", ExecutiveDashboard);



/** @odoo-module **/

import { Component, onWillStart, onMounted, onWillUnmount, useState, useRef } from '@odoo/owl';
import { registry } from '@web/core/registry';
import { rpc } from "@web/core/network/rpc";
import { loadJS } from "@web/core/assets";

class ExecutiveDashboard extends Component {
    static template = 'executive_dashboard.Template';

    setup() {
        this.state = useState({ data: null, loading: true });
        this.refreshInterval = null;

        this.sportChartRef = useRef("sportChart");
        this.trendChartRef = useRef("trendChart");
        this.topDaysChartRef = useRef("topDaysChart");
        this.topGroundsChartRef = useRef("topGroundsChart");
        this.monthlyRevenueChartRef = useRef("monthlyRevenueChart");

        this.sportChart = null;
        this.trendChart = null;
        this.topDaysChart = null;
        this.topGroundsChart = null;
        this.monthlyRevenueChart = null;

        onWillStart(async () => {
            await loadJS('/web/static/lib/Chart/Chart.js');
            await this.fetchDashboardData();
        });

        onMounted(() => {
            this.renderCharts();
            this.refreshInterval = setInterval(() => {
                this.fetchDashboardData(true);
            }, 60000); // refresh every 60 seconds
        });

        onWillUnmount(() => {
            clearInterval(this.refreshInterval);
            this.destroyCharts();
        });
    }

    async fetchDashboardData(isRefresh = false) {
        try {
            const result = await rpc('/executive_dashboard/data', {});
            this.state.data = result;
            this.state.loading = false;

            if (isRefresh) {
                this.renderCharts();
            }
        } catch (error) {
            console.error("Error fetching dashboard data:", error);
        }
    }

    destroyCharts() {
        if (this.sportChart) this.sportChart.destroy();
        if (this.trendChart) this.trendChart.destroy();
        if (this.topDaysChart) this.topDaysChart.destroy();
        if (this.topGroundsChart) this.topGroundsChart.destroy();
        if (this.monthlyRevenueChart) this.monthlyRevenueChart.destroy();
    }

    renderCharts() {
        if (!this.state.data) return;

        const { sports_data, trend_data, top_booking_days, top_grounds_data, monthly_revenue } = this.state.data;
        console.log(trend_data);

        this.destroyCharts();

        const sportCtx = this.sportChartRef.el;
        if (sportCtx && Object.keys(sports_data).length > 0) {
            this.sportChart = new Chart(sportCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(sports_data),
                    datasets: [{
                        data: Object.values(sports_data),
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' },
                        title: { display: true, text: 'Sports Distribution Today' }
                    }
                }
            });
        }

        const trendCtx = this.trendChartRef.el;
        if (trendCtx && trend_data.length > 0) {
            this.trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: trend_data.map(d => d.day),
                    datasets: [{
                        label: 'Bookings per Day',
                        data: trend_data.map(d => d.count),
                        borderColor: '#36A2EB',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { beginAtZero: true, ticks: { stepSize: 1 } },
                        x: {}
                    },
                    plugins: {
                        title: { display: true, text: 'Booking Trend (Last 7 Days)' }
                    }
                }
            });
        }

        const topDaysCtx = this.topDaysChartRef.el;
        if (topDaysCtx && top_booking_days.length > 0) {
            this.topDaysChart = new Chart(topDaysCtx, {
                type: 'bar',
                data: {
                    labels: top_booking_days.map(d => d.date),
                    datasets: [{
                        label: 'Number of Bookings',
                        data: top_booking_days.map(d => d.count),
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { beginAtZero: true, ticks: { stepSize: 1 } },
                        x: {}
                    },
                    plugins: {
                        title: { display: true, text: 'Top 7 High Booking Days (Last 30 Days)' }
                    }
                }
            });
        }

        const topGroundsCtx = this.topGroundsChartRef.el;
        if (topGroundsCtx && top_grounds_data.length > 0) {
            this.topGroundsChart = new Chart(topGroundsCtx, {
                type: 'bar',
                data: {
                    labels: top_grounds_data.map(d => d.ground),
                    datasets: [{
                        label: 'Bookings',
                        data: top_grounds_data.map(d => d.bookings),
                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    indexAxis: 'y',
                    plugins: {
                        title: { display: true, text: 'Top Booking Grounds (Last 30 Days)' }
                    },
                    scales: {
                        x: { beginAtZero: true },
                        y: {}
                    }
                }
            });
        }

        const monthlyRevenueCtx = this.monthlyRevenueChartRef.el;
        if (monthlyRevenueCtx && monthly_revenue.length > 0) {
            this.monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
                type: 'bar',
                data: {
                    labels: monthly_revenue.map(d => d.month),
                    datasets: [{
                        label: 'Revenue ($)',
                        data: monthly_revenue.map(d => d.revenue),
                        backgroundColor: 'rgba(153, 102, 255, 0.6)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: { display: true, text: 'Monthly Revenue Comparison (Last 6 Months)' },
                        tooltip: {
                            callbacks: {
                                label: ctx => 'Revenue: $' + ctx.parsed.y.toLocaleString()
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: val => '$' + val.toLocaleString()
                            }
                        },
                        x: {}
                    }
                }
            });
        }
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }
}

registry.category("actions").add("executive_dashboard_tag", ExecutiveDashboard);
