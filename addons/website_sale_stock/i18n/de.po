# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "<span invisible=\"not show_availability\">Units</span>"
msgstr "<span invisible=\"not show_availability\">Einheiten</span>"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product_card/product_card.js:0
msgid "All available quantity selected"
msgstr "Alle verfügbaren Mengen ausgewählt"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "All warehouses"
msgstr "Alle Lagerhäuser"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__stock_notification_partner_ids
msgid "Back in stock Notifications"
msgstr "„Wieder vorrätig“-Benachrichtigungen"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "Verkauf fortsetzen"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "Verkauf fortsetzen, wenn keine Ware mehr vorrätig ist"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Dear Customer,"
msgstr "Lieber Kunde,"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"Standardmodus für die Verfügbarkeit, der für neue lagerfähige Produkte "
"gesetzt wird. Dies kann auf Produktebene wieder geändert werden."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "Standardsichtbarkeit für benutzerdefinierte Nachrichten."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Get notified when back in stock"
msgstr "Erhalten Sie eine Benachrichtigung, wenn wieder vorrätig"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "How to display products having low quantities (on hand - reserved)"
msgstr ""
"Wie werden Produkte mit geringem Bestand angezeigt (vorrätig - reserviert)?"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/controllers/main.py:0
msgid "Invalid Email"
msgstr "Ungültige E-Mail"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Invalid email"
msgstr "Ungültige E-Mail"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory Defaults"
msgstr "Standardeinstellungen für Lager"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Only"
msgstr "Nur"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Order Now"
msgstr "Jetzt bestellen"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "Out of Stock"
msgstr "Nicht vorrätig"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product/product.xml:0
msgid "Out of stock"
msgstr "Nicht vorrätig"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "Nicht vorrätig"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "„Nicht vorrätig“-Nachricht"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_combo
msgid "Product Combo"
msgstr "Produktkombi"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: website_sale_stock
#: model:ir.actions.server,name:website_sale_stock.ir_cron_send_availability_email_ir_actions_server
msgid "Product: send email regarding products availability"
msgstr "Produkt: E-Mails bezüglich Produktverfügbarkeit versenden"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "Regards,"
msgstr "Freundliche Grüße"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/product_card/product_card.xml:0
msgid "Requested quantity not available"
msgstr "Geforderte Menge nicht verfügbar"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "Verfügbare Menge anzeigen"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "Schwelle anzeigen"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "Mengenverfügbarkeit anzeigen"

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"Manche Produkte sind zurzeit nicht auf Lager. Ihr Warenkorb wurde "
"aktualisiert. Entschuldigen Sie die Unannehmlichkeiten."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.availability_email_body
msgid "The following product is now available."
msgstr "Das folgende Produkt ist jetzt verfügbar."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid "The item has not been added to your cart since it is not available."
msgstr ""
"Der Artikel wurde nicht in Ihren Warenkorb gelegt, da er nicht verfügbar "
"ist."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/product_product.py:0
msgid "The product '%(product_name)s' is now available"
msgstr "Das Produkt „%(product_name)s“ ist jetzt verfügbar"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "This product is out of stock."
msgstr "Dieses Produkt ist nicht vorrätig."

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Units"
msgstr "Einheiten"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "Lagerhaus"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "We'll notify you once the product is back in stock."
msgstr ""
"Wir werden Sie benachrichtigen, sobald das Produkt wieder vorrätig ist."

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "Website"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website where this order has been placed, for eCommerce orders."
msgstr ""
"Website, auf der diese Bestellung für E-Commerce-Aufträge aufgegeben wurde"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added"
msgstr "Sie haben bereits "

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "You already added all the available product in your cart."
msgstr "Alle verfügbaren Produkte wurden bereits Ihrem Warenkorb hinzugefügt."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You already have %s Units in your cart."
msgstr "Sie haben bereits %s Einheiten in Ihrem Warenkorb."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order_line.py:0
msgid ""
"You ask for %(desired_qty)s %(product_name)s but only %(new_qty)s is "
"available"
msgstr ""
"Sie möchten %(desired_qty)s %(product_name)s, aber nur %(new_qty)s sind "
"verfügbar."

#. module: website_sale_stock
#. odoo-python
#: code:addons/website_sale_stock/models/sale_order.py:0
msgid "You ask for %(desired_qty)s products but only %(new_qty)s is available"
msgstr ""
"Sie möchten %(desired_qty)s Produkte, aber nur %(new_qty)s sind verfügbar."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/js/website_sale_reorder.js:0
msgid "You ask for %(quantity1)s Units but only %(quantity2)s are available."
msgstr ""
"Sie haben %(quantity1)s Produkte angefragt, aber nur %(quantity2)s sind "
"verfügbar."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "in your cart."
msgstr "in Ihrem Warenkorb."

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "left in stock."
msgstr "auf Lager."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "nur, wenn weniger als"

#. module: website_sale_stock
#. odoo-javascript
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
msgid "<EMAIL>"
msgstr "<EMAIL>"
