# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_worldline
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Key"
msgstr "API key"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Secret"
msgstr "API Secret"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Kan geen verbinding maken met de API."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Geen transactie gevonden die overeenkomt met referentie %s."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "PSPID"
msgstr "PSPID"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_provider
msgid "Payment Provider"
msgstr "Betaalprovider"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransactie"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "Gegevens ontvangen met ontbrekende betalingsstatus."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Gegevens ontvangen met ontbrekende referentie %(ref)s."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid ""
"Received invalid transaction status %(status)s with error code "
"%(error_code)s."
msgstr ""
"Ontvangen ongeldige transactiestatus %(status)s met foutcode %(error_code)s."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr "De communicatie met de API is mislukt. Details: %s"

#. module: payment_worldline
#: model:ir.model.fields,help:payment_worldline.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "De technische code van deze betaalprovider."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "De transactie is niet gekoppeld aan een token."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Transaction cancelled with error code %(error_code)s."
msgstr "Transactie geannuleerd met foutcode %(error_code)s."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Transaction declined with error code %(error_code)s."
msgstr "Transactie geweigerd met foutcode %(error_code)s."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Key"
msgstr "Webhook sleutel"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Secret"
msgstr "Webhook geheim"

#. module: payment_worldline
#: model:ir.model.fields.selection,name:payment_worldline.selection__payment_provider__code__worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_key
msgid "Worldline API Key"
msgstr "Worldline API sleutel"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_secret
msgid "Worldline API Secret"
msgstr "Worldline API Geheim"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_pspid
msgid "Worldline PSPID"
msgstr "PSPID Wereldlijn"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_key
msgid "Worldline Webhook Key"
msgstr "Worldline Webhook Sleutel"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_secret
msgid "Worldline Webhook Secret"
msgstr "Worldline Webhook Geheim"
