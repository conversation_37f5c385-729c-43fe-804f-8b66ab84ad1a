# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Tipe perusahaan</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Didirikan</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Teknologi yang Digunakan</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Zona waktu</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sektor-Sektor</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Perkiraan pendapatan</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Telepon</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>X</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>X</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Karyawan-Karyawan</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> per tahun</span>"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction
msgid "Action Needed"
msgstr "Action Dibutuhkan"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_attachment_count
msgid "Attachment Count"
msgstr "Jumlah Lampiran"

#. module: iap_mail
#. odoo-javascript
#: code:addons/iap_mail/static/src/js/services/iap_notification_service.js:0
msgid "Buy more credits"
msgstr "Beli lebih banyak kredit"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__company_ids
msgid "Company"
msgstr "Perusahaan"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Penerima Peringatan Email"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Threshold Peringatan Email"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_follower_ids
msgid "Followers"
msgstr "Pengikut-Pengikut"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Partner)"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: iap_mail
#: model:ir.model,name:iap_mail.model_iap_account
msgid "IAP Account"
msgstr "Akun IAP"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Bila dicentang, pesan baru memerlukan perhatian Anda."

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Bila dicentang, beberapa pesan mungkin memiliki kesalahan pengiriman."

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_is_follower
msgid "Is Follower"
msgstr "Apakah Pengikut"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error
msgid "Message Delivery error"
msgstr "Error Pengiriman Pesan"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_ids
msgid "Messages"
msgstr "Pesan-Pesan"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah error"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan action"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan error pengiriman"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "follower"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "http://www.twitter.com/"
msgstr "http://www.twitter.com/"
