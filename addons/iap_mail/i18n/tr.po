# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# emre oktem, 2024
# Melih Melik Sonmez, 2024
# <PERSON><PERSON> Altinisik <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Murat <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Şirket türü</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Bulundu</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Teknolojiler Kullanıldı</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>E-posta</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Zaman Dilimi</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sektörler</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Tahmini gelir</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Telefon</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>X</b>"
msgstr ""

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Çalışanlar</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> yıl başına</span>"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction
msgid "Action Needed"
msgstr "Aksiyon Gerekiyor"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: iap_mail
#. odoo-javascript
#: code:addons/iap_mail/static/src/js/services/iap_notification_service.js:0
msgid "Buy more credits"
msgstr "Daha fazla kredi al"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__company_ids
msgid "Company"
msgstr "Firma"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr ""

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr ""

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: iap_mail
#: model:ir.model,name:iap_mail.model_iap_account
msgid "IAP Account"
msgstr "IAP Hesabı"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Aksiyon Sayısı"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "İşlem gerektiren mesaj sayısı"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "takipçiler"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "http://www.twitter.com/"
msgstr ""
