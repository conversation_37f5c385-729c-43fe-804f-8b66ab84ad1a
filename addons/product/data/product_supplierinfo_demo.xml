<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="product_supplierinfo_1" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_6_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">750</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_2" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_6_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">790</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_2bis" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_6_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">3</field>
        <field name="min_qty">3</field>
        <field name="price">785</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_3" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_7_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">13.0</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_4" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_7_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">14.4</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_5" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_8_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">2</field>
        <field name="min_qty">5</field>
        <field name="price">1299</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_6" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_8_product_template"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="delay">4</field>
        <field name="min_qty">1</field>
        <field name="price">1399</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_7" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_10_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">2</field>
        <field name="min_qty">1</field>
        <field name="price">120.50</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_8" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_11_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">2</field>
        <field name="min_qty">1</field>
        <field name="price">28</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_9" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_13_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">5</field>
        <field name="min_qty">1</field>
        <field name="price">78</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_10" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_16_product_template"/>
        <field name="partner_id" ref="base.res_partner_3"/>
        <field name="delay">1</field>
        <field name="min_qty">1</field>
        <field name="price">20</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_12" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_20_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">1700</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_13" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_20_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">4</field>
        <field name="min_qty">5</field>
        <field name="price">1720</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_14" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_22_product_template"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">2010</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_15" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_24_product_template"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="delay">3</field>
        <field name="min_qty">1</field>
        <field name="price">876</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_16" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_acoustic_bloc_screens"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">8</field>
        <field name="min_qty">1</field>
        <field name="price">287</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_17" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_delivery_02_product_template"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="delay">4</field>
        <field name="min_qty">1</field>
        <field name="price">390</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_18" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
        <field name="partner_id" ref="base.res_partner_3"/>
        <field name="delay">2</field>
        <field name="min_qty">12</field>
        <field name="price">90</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_19" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">4</field>
        <field name="min_qty">1</field>
        <field name="price">66</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_20" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_delivery_02_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">5</field>
        <field name="min_qty">1</field>
        <field name="price">35</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_21" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="delay">7</field>
        <field name="min_qty">1</field>
        <field name="price">55</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_22" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_9_product_template"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="delay">4</field>
        <field name="min_qty">0</field>
        <field name="price">10</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_23" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_27_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">10</field>
        <field name="min_qty">0</field>
        <field name="price">95.50</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_24" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_12_product_template"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="delay">3</field>
        <field name="min_qty">0</field>
        <field name="price">120.50</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_25" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_12_product_template"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="delay">2</field>
        <field name="min_qty">0</field>
        <field name="price">130.50</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

    <record id="product_supplierinfo_26" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_product_5_product_template"/>
        <field name="partner_id" ref="base.res_partner_10"/>
        <field name="delay">1</field>
        <field name="min_qty">0</field>
        <field name="price">145</field>
        <field name="currency_id" ref="base.USD"/>
    </record>

</odoo>
