# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_razorpay
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__all
msgid "All"
msgstr "Semua"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__bharatqr
msgid "BHARATQR"
msgstr "BHARATQR"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/razorpay_pos_request.py:0
msgid "Cannot decode Razorpay POS response"
msgstr "Tidak dapat decode tanggapan Razorpay POS"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Tidak dapat memproses transaksi dengan jumlah negatif."

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__card
msgid "Card"
msgstr "Kartu"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid ""
"Choose allow payment mode: \n"
" All/Card/UPI or QR"
msgstr ""
"Pilih mode pembayaran yang diizinkan: \n"
" Semua/Kartu/UPI atau QR"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Tidak dapat terhubung ke server Odoo, mohon periksa koneksi internet Anda "
"dan coba lagi."

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid ""
"Device Serial No \n"
" ex: 7000012300"
msgstr ""
"Nomor Seri Perangkat \n"
" contoh: 7000012300"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid ""
"Payment has been queued. You may choose to wait for the payment to initiate "
"on terminal or proceed to cancel this transaction"
msgstr ""
"Pembayaran telah dimasukkan antrian. Anda dapat memilih untuk menunggu "
"pembayaran untuk muncul di terminal atau lanjutkan untuk membatalkan "
"transaksi ini"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metode Pembayaran Point of Sale POS"

#. module: pos_razorpay
#: model:ir.model,name:pos_razorpay.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pembayaran POS"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid "Razorpay API Key"
msgstr "Razorpay API Key"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_allowed_payment_modes
msgid "Razorpay Allowed Payment Modes"
msgstr "Razorpay Mode Pembayaran yang Diizinkan"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_tid
msgid "Razorpay Device Serial No"
msgstr "Razorpay Nomor Seri Perangkat"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Razorpay Error"
msgstr "Razorpay Error"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment cancel request expected errorCode not found in the "
"response"
msgstr ""
"Permintaan membatalkan pembayaran Razorpay POS mengharapkan errorCode not "
"found di tanggapan"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment request expected errorCode not found in the response"
msgstr ""
"Permintaan pembayaran Razorpay POS mengharapkan errorCode not found di "
"tanggapan"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid ""
"Razorpay POS payment status request expected errorCode not found in the "
"response"
msgstr ""
"Permintaan status pembayaran Razorpay POS mengharapkan errorCode not found "
"di tanggapan"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction canceled successfully"
msgstr "Transaksi Razorpay POS sukses dibatalkan"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "Razorpay POS transaction failed"
msgstr "Transaksi Razorpay POS gagal"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment__razorpay_reverse_ref_no
msgid "Razorpay Reverse Reference No."
msgstr "Putar Balikkan Nomor Referensi Razorpay."

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Razorpay Test Mode"
msgstr "Mode Testing Razorpay"

#. module: pos_razorpay
#: model:ir.model.fields,field_description:pos_razorpay.field_pos_payment_method__razorpay_username
msgid "Razorpay Username"
msgstr "Username Razorpay"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Reference number mismatched"
msgstr "Nomor referensi tidak cocok"

#. module: pos_razorpay
#. odoo-python
#: code:addons/pos_razorpay/models/pos_payment_method.py:0
msgid "This Payment Terminal is only valid for INR Currency"
msgstr "Terminal Pembayaran ini hanya valid untuk mata uang INR"

#. module: pos_razorpay
#. odoo-javascript
#: code:addons/pos_razorpay/static/src/app/payment_razorpay.js:0
msgid "Transaction failed due to inactivity"
msgstr "Transaksi gagal oleh karena ketidakaktifan"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_test_mode
msgid "Turn it on when in Test Mode"
msgstr "Nyalakan saat di Mode Test"

#. module: pos_razorpay
#: model:ir.model.fields.selection,name:pos_razorpay.selection__pos_payment_method__razorpay_allowed_payment_modes__upi
msgid "UPI"
msgstr "UPI"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_api_key
msgid ""
"Used when connecting to Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"
msgstr ""
"Digunakan saat mencoba terhubung ke Razorpay: "
"https://razorpay.com/docs/payments/dashboard/account-settings/api-keys/"

#. module: pos_razorpay
#: model:ir.model.fields,help:pos_razorpay.field_pos_payment_method__razorpay_username
msgid ""
"Username(Device Login) \n"
" ex: **********"
msgstr ""
"Username(Login Perangkat) \n"
" contoh: **********"
