# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_gstin_status
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-24 08:17+0000\n"
"PO-Revision-Date: 2025-01-24 08:17+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid ""
" Warning: You are currently in a test environment. The result is a dummy."
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.move_form_inherit_l10n_in_gst_verification
msgid ""
"<span invisible=\"l10n_in_gstin_verified_date\">Not Checked</span>\n"
"                            <span invisible=\"not l10n_in_gstin_verified_date\" class=\"ps-3\">Checked: </span>"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.l10n_in_view_partner_base_vat_form
msgid ""
"<span invisible=\"not l10n_in_gstin_verified_date or not l10n_in_gstin_verified_status\" class=\"oe_inline text-success\">Active</span>\n"
"                        <span invisible=\"not l10n_in_gstin_verified_date or l10n_in_gstin_verified_status\" class=\"oe_inline text-danger\">Inactive</span>"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.move_form_inherit_l10n_in_gst_verification
msgid ""
"<span invisible=\"not l10n_in_partner_gstin_status\" class=\"oe_inline text-success\">Active</span>\n"
"                        <span invisible=\"not l10n_in_gstin_verified_date or l10n_in_partner_gstin_status\" class=\"oe_inline text-danger\">Inactive</span>"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.l10n_in_view_partner_base_vat_form
msgid "Check GSTIN status"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.l10n_in_view_partner_base_vat_form
msgid "Check Status"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model,name:l10n_in_gstin_status.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_bank_statement_line__l10n_in_partner_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_move__l10n_in_partner_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_res_partner__l10n_in_gstin_verified_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_res_users__l10n_in_gstin_verified_status
msgid "GST Status"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "GSTIN %(vat)s is %(status)s and Effective from %(date_from)s."
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "GSTIN %(vat)s is %(status)s, effective date is not available."
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "GSTIN Status Updated Successfully"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_res_partner__l10n_in_gstin_verified_date
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_res_users__l10n_in_gstin_verified_date
msgid "GSTIN Verified Date"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model,name:l10n_in_gstin_status.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_bank_statement_line__l10n_in_gstin_verified_date
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_move__l10n_in_gstin_verified_date
msgid "L10N In Gstin Verified Date"
msgstr ""

#. module: l10n_in_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_bank_statement_line__l10n_in_show_gstin_status
#: model:ir.model.fields,field_description:l10n_in_gstin_status.field_account_move__l10n_in_show_gstin_status
msgid "L10N In Show Gstin Status"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "Not enough credits to check GSTIN status"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "Please enter the GSTIN"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.l10n_in_view_partner_base_vat_form
msgid "Reverify GSTIN status"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid ""
"Something went wrong while fetching the GST status.Please Contact Support if"
" the error persists withResponse: %(response)s"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "The provided GSTIN is invalid. Please check the GSTIN and try again."
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "Unable to connect with GST network"
msgstr ""

#. module: l10n_in_gstin_status
#: model_terms:ir.ui.view,arch_db:l10n_in_gstin_status.move_form_inherit_l10n_in_gst_verification
msgid "Verify GSTIN status"
msgstr ""

#. module: l10n_in_gstin_status
#. odoo-python
#: code:addons/l10n_in_gstin_status/models/res_partner.py:0
msgid "You must be logged in an Indian company to use this feature"
msgstr ""
