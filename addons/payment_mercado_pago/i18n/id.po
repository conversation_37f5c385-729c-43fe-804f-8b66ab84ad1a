# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mercado_pago
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> Many<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_mercado_pago
#: model_terms:ir.ui.view,arch_db:payment_mercado_pago.payment_provider_form
msgid "Access Token"
msgstr "Token Akses"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"Call your card issuer to activate your card or use another payment method. "
"The phone number is on the back of your card."
msgstr ""
"Panggil penerbit kartu Anda untuk mengaktifkan Anda atau gunakan metode "
"pembayaran lain. Nomor telepon ada di bagian belakang kartu Anda."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Check expiration date."
msgstr "Periksa tanggal kadaluwarsa."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Check the card number."
msgstr "Periksa nomor kartu"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Check the card security code."
msgstr "Periksa kode keamanan kartu."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Check the data."
msgstr "Periksa data."

#. module: payment_mercado_pago
#: model:ir.model.fields,field_description:payment_mercado_pago.field_payment_provider__code
msgid "Code"
msgstr "Kode"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Tidak dapat membuat hubungan ke API."

#. module: payment_mercado_pago
#: model:ir.model.fields.selection,name:payment_mercado_pago.selection__payment_provider__code__mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment_mercado_pago
#: model:ir.model.fields,field_description:payment_mercado_pago.field_payment_provider__mercado_pago_access_token
msgid "Mercado Pago Access Token"
msgstr "Token Akses Mercado Pago"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Tidak ada transaksi dengan referensi %s yang cocok."

#. module: payment_mercado_pago
#: model:ir.model,name:payment_mercado_pago.model_payment_provider
msgid "Payment Provider"
msgstr "Penyedia Pembayaran"

#. module: payment_mercado_pago
#: model:ir.model,name:payment_mercado_pago.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaksi Tagihan"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Payment was not processed, use another card or contact issuer."
msgstr "Pembayaran tidak diproses, gunakan kartu lain atau hubungi penerbit."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_transaction.py:0
msgid "Received data with invalid status: %s"
msgstr "Menerima data dengan status tidak valid: %s"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_transaction.py:0
msgid "Received data with missing payment id."
msgstr "Menerima data tanpa id pembayaran."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr "Menerima data dengan referensi yang kurang lengkap."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_transaction.py:0
msgid "Received data with missing status."
msgstr "Menerima data tanpa status."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Mercado Pago gave us the following "
"information: '%(error_message)s' (code %(error_code)s)"
msgstr ""
"Komunikasi dengan API gagal. Mercado Pago memberikan kita informasi berikut:"
" '%(error_message)s' (kode %(error_code)s)"

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/models/payment_provider.py:0
msgid ""
"The communication with the API failed. The response is empty. Please verify "
"your access token."
msgstr ""
"Komunikasi dengan API gagal. Tanggapan kosong. Mohon verifikasi token akses "
"Anda."

#. module: payment_mercado_pago
#: model:ir.model.fields,help:payment_mercado_pago.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Kode teknis penyedia pembayaran ini."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "This payment method does not process payments in installments."
msgstr "Metode pembayaran ini tidak memproses pembayaran secara cicilan."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"We are processing your payment. Don't worry, in less than 2 business days, "
"we will notify you by e-mail if your payment has been credited."
msgstr ""
"Kami sedang memproses pembayaran Anda. Jangan khawatir, dalam 2 hari kerja, "
"kami akan menotifikasi Anda melalui email bila pembayaran berhasil."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"We are processing your payment. Don't worry, less than 2 business days we "
"will notify you by e-mail if your payment has been credited or if we need "
"more information."
msgstr ""
"Kami sedang memproses pembayaran Anda. Jangan khawatir, dalam 2 hari kerja, "
"kami akan menotifikasi Anda melalui email bila pembayaran berhasil atau bila"
" kami membutuhkan lebih banyak informasi."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"We were unable to process your payment, please check your card information."
msgstr ""
"Kami tidak dapat memproses pembayaran Anda, silakan periksa informasi kartu "
"Anda."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "We were unable to process your payment, please use another card."
msgstr ""
"Kami tidak dapat memproses pembayaran Anda, silakan gunakan kartu lain."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"You have already made a payment for that value. If you need to pay again, "
"use another card or another payment method."
msgstr ""
"Anda sudah melakukan pembayaran untuk nominal tersebut. Bila Anda harus "
"membayar lagi, bayar mengunakan metode pembayaran lain."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"You have reached the limit of allowed attempts. Choose another card or other"
" means of payment."
msgstr ""
"Anda telah mencapai batas percobaan yang diizinkan. Pilih kartu lain atau "
"cara pembayaran lainnya."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "You must authorize the payment with this card."
msgstr "Anda harus mengotorisasi pembayaran dengan kartu ini."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid "Your card has not enough funds."
msgstr "Saldo kartu Anda tidak cukup."

#. module: payment_mercado_pago
#. odoo-python
#: code:addons/payment_mercado_pago/const.py:0
msgid ""
"Your payment has been credited. In your summary you will see the charge as a"
" statement descriptor."
msgstr ""
"Pembayaran Anda telah berhasil. Pada ringkasan Anda akan melihat biaya "
"sebagai deskriptor laporan."
