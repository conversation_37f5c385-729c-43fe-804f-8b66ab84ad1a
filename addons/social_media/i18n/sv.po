# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_media
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_media
#: model:ir.model,name:social_media.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_facebook
msgid "Facebook Account"
msgstr "Facebook-konto"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_github
msgid "GitHub Account"
msgstr "GitHub-konto"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_instagram
msgid "Instagram Account"
msgstr "Instagramkonto"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn-konto"

#. module: social_media
#: model_terms:ir.ui.view,arch_db:social_media.view_company_form_inherit_social_media
msgid "Social Media"
msgstr "Sociala medier"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_tiktok
msgid "TikTok Account"
msgstr "TikTok-konto"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_twitter
msgid "X Account"
msgstr "X-konto"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_youtube
msgid "Youtube Account"
msgstr "Youtube-konto"
