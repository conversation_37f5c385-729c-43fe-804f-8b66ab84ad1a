# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
#
# Translators:
# Q<PERSON>jo<PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Qaidjohar <PERSON>bha<PERSON>, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Buy more credits"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Companies"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_company.py:0
msgid "Company auto-completed by Odoo Partner Autocomplete Service"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Config Settings"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Created by"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Created on"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Display Name"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "IAP Account Token missing"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "No account token"
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "Not enough credits for Partner Autocomplete"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Partner"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr ""

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
msgid "Partner Autocomplete: Sync with remote DB"
msgstr ""

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_partner.py:0
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Placeholder"
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_fieldchar.js:0
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
msgid "Searching Autocomplete..."
msgstr ""

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Set Your Account Token"
msgstr ""

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "Test mode"
msgstr ""
