<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- The reveal service is defined in the iap module, as a lot of different modules depend on it. -->
        <record id="iap_service_reveal" model="iap.service">
            <field name="name">Lead Generation</field>
            <field name="technical_name">reveal</field>
            <field name="description">Get quality leads and opportunities: convert your website visitors into leads, generate leads based on a set of criteria and enrich the company data of your opportunities.</field>
            <field name="unit_name">Credits</field>
            <field name="integer_balance">True</field>
        </record>
    </data>
</odoo>
