# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_online_payment
# 
# Translators:
# <PERSON>, 2025
# Naser mars, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Naser mars, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "<strong>Error:</strong> The currency is missing or invalid."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid ""
"<strong>Error:</strong> There was a problem during the payment process."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_config.py:0
msgid "A POS config cannot have more than one online payment method."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "A payment option must be specified."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid ""
"A validation payment cannot be used for a Point of Sale online payment."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "All available providers"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
msgid ""
"All payment providers configured for an online payment method must use the "
"same currency as the Sales Journal, or the company currency if that is not "
"set, of the POS config."
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__online_payment_provider_ids
msgid "Allowed Providers"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Amount"
msgstr "مقدار"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/online_payment_popup/online_payment_popup.xml:0
msgid "Amount:"
msgstr "مقدار"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "Cancel payment"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
msgid "Cannot create a POS online payment without an accounting payment."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
msgid ""
"Cannot create a POS payment with a not online payment method and an online "
"accounting payment."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
msgid "Cannot edit a POS online payment essential data."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
msgid ""
"Could not create an online payment method (company_id=%(company_id)d, "
"pos_config_id=%(pos_config_id)d)"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__has_an_online_payment_provider
msgid "Has An Online Payment Provider"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Invalid online payment"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Invalid online payments"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Invoice could not be generated"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_order__next_online_payment_amount
msgid "Next online payment amount to pay"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields.selection,name:pos_online_payment.selection__pos_payment_method__type__online
msgid "Online"
msgstr "آنلاین"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__is_online_payment
msgid "Online Payment"
msgstr "پرداخت آنلاین"

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_order__online_payment_method_id
msgid "Online Payment Method"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment__online_account_payment_id
msgid "Online accounting payment"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Online payment unavailable"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Online payments cannot have a negative amount (%s: %s)."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Order ID"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "Order ID:"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Order Reference"
msgstr "مرجع سفارش"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/online_payment_popup/online_payment_popup.xml:0
msgid "Order reference:"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Order saving issue"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/account_payment.py:0
#: code:addons/pos_online_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:pos_online_payment.field_account_payment__pos_order_id
#: model:ir.model.fields,field_description:pos_online_payment.field_payment_transaction__pos_order_id
#: model_terms:ir.ui.view,arch_db:pos_online_payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:pos_online_payment.view_account_payment_form_inherit_pos_online_payment
msgid "POS Order"
msgstr "سفارش POS"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "Payment Providers"
msgstr "سرویس دهندگان پرداخت"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "تراکنش پرداخت"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_account_payment
msgid "Payments"
msgstr "پرداخت‌ها"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_config
msgid "Point of Sale Configuration"
msgstr "پیکربندی پایانه فروش"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارشات پایانه فروش"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "روش های پرداخت پایانه فروش"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_payment
msgid "Point of Sale Payments"
msgstr "پرداخت های پایانه فروش"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_session
msgid "Point of Sale Session"
msgstr "نشست پایانه فروش"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Processed by"
msgstr "پرادازش شده توسط"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/online_payment_popup/online_payment_popup.xml:0
msgid "QR Code to pay"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/online_payment_popup/online_payment_popup.xml:0
msgid "Scan the QR code to pay"
msgstr "<p>کد QR را برای پرداخت اسکن کنید</p>"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/online_payment_popup/online_payment_popup.xml:0
msgid "Scan to pay"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Server error"
msgstr "خطای سرور"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
msgid "The POS online payment (tx.id=%d) could not be saved correctly"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
msgid ""
"The POS online payment (tx.id=%d) could not be saved correctly because the "
"online payment method could not be found"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The POS session is not opened."
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_payment_transaction__pos_order_id
msgid "The Point of Sale order linked to the payment transaction"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_account_payment__pos_order_id
msgid "The Point of Sale order linked to this payment"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "The QR Code for paying could not be generated."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The amount to pay has changed. Please refresh the page."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The currency is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "The invoice could not be generated."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The order has been cancelled."
msgstr "سفارش لغو شده است."

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "The order has not been saved correctly on the server."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_session.py:0
msgid "The partner of the POS online payment (id=%d) could not be found"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The payment provider is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"پرداخت باید به صورت مستقیم، با تغییر مسیر، یا از طریق یک توکن انجام شود."

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The payment token is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
msgid "The payment transaction (%d) has a negative amount."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The provided order or access token is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "The provided partner_id is different than expected."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "The saved order could not be retrieved."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid ""
"The total amount of remaining online payments to execute (%s) doesn't "
"correspond to the remaining unpaid amount of the order (%s)."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "There are online payments that were missing in your view."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid ""
"There is a problem with the server. The order online payment status cannot "
"be retrieved."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid ""
"There is a problem with the server. The order online payment status cannot "
"be retrieved. Are you sure there is no online payment for this order ?"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid ""
"There is no online payment method configured for this Point of Sale order."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "There is nothing to pay for this order."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "There is nothing to pay."
msgstr "هیچ موردی برای پرداخت وجود ندارد."

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "To Pay"
msgstr "قابل پرداخت"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_config.py:0
msgid ""
"To use an online payment method in a POS config, it must have at least one "
"published payment provider supporting the currency of that POS config."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
msgid "Tokenization is not available for logged out customers."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Transaction Reference"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Try again"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__type
msgid "Type"
msgstr "نوع"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Updated online payments"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_pos_payment_method__is_online_payment
msgid ""
"Use this payment method for online payments (payments made on a web page "
"with online payment providers)"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/overrides/pos_overrides/components/payment_screen/payment_screen.js:0
msgid "Yes"
msgstr "بله"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "You have not activated any"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "payment provider"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "to allow online payments."
msgstr ""
