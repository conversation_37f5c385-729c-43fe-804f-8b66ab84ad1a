# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* link_tracker
# 
# Translators:
# W<PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"點擊數目\" role=\"img\"/>"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "<span class=\"o_stat_text\">Visit Page</span>"
msgstr "<span class=\"o_stat_text\">訪問頁面</span>"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__absolute_url
msgid "Absolute URL"
msgstr "絕對網址"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__label
msgid "Button label"
msgstr "按鈕備註"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Campaign"
msgstr "行銷活動"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_click_action_statistics
msgid "Click Statistics"
msgstr "點擊率統計"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "Clicks"
msgstr "點選"

#. module: link_tracker
#: model:ir.model.constraint,message:link_tracker.constraint_link_tracker_code_code
msgid "Code must be unique."
msgstr "代碼必須是唯一的。"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr "代碼"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
msgid ""
"Combinations of Link Tracker values (URL, campaign, medium, source, and label) must be unique.\n"
"The following combinations are already used: \n"
"- %(error_lines)s"
msgstr ""
"連結追蹤工具的數值（網址、推廣活動、媒介、來源及標籤），其組合必須獨一無二。\n"
"以下組合已被使用：\n"
"- %(error_lines)s"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Country"
msgstr "國家"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid "Create a link tracker"
msgstr "建立短網址連結跟蹤器"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "建立於"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
msgid "Creating a Link Tracker without URL is not possible"
msgstr "無法建立未設定 URL 的連結追蹤"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Group By"
msgstr "分組依據"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr "短網址主機"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "識別號"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr "網路協議"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Link"
msgstr "連結"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_form
msgid "Link Click"
msgstr "點選連結"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_graph
msgid "Link Clicks"
msgstr "連結點擊次數"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.link_tracker_menu_main
msgid "Link Tracker"
msgstr "連結追蹤"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "點選連結追蹤器"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr "連結跟蹤程式碼"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Links"
msgstr "連結"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_tree
msgid "Links Clicks"
msgstr "連結點擊次數"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "郵件渲染混合"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Medium"
msgstr "媒體"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_click_action_statistics
msgid "No data yet!"
msgstr "暫無資料！"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr "點選次數"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_utm_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "行銷活動產生的點擊次數"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "網頁標題"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr "重導向網址"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr "短網址代碼"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr "短網址代碼"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Source"
msgstr "來源"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action_campaign
msgid "Statistics of Clicks"
msgstr "點選分析"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr "目標網址"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
#: model:ir.model.fields,help:link_tracker.field_link_tracker_click__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr "這是一個幫助您追蹤不同行銷活動的名稱，例如，Fall_Drive、Christmas_Special"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "傳遞方式，例：明信片、電子郵件、橫幅廣告"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "連結的來源，例：搜尋引擎、其他網站，或電郵清單的名稱"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Title and URL"
msgstr "標題和網址"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Tracked URL"
msgstr "追蹤網址"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid ""
"Trackers are used to collect count stat about click on links and generate "
"short URLs."
msgstr "跟蹤器用於收集有關點擊該連結的統計資訊並生成短網址。"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "URL"
msgstr "網址"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "UTM"
msgstr "UTM"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_utm_campaign
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__campaign_id
msgid "UTM Campaign"
msgstr "UTM 行銷活動"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Visit Page"
msgstr "訪問頁面"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
msgid "Visit Webpage"
msgstr "訪問網頁"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Website Link"
msgstr "網站連結"

#. module: link_tracker
#. odoo-python
#: code:addons/link_tracker/models/link_tracker.py:0
msgid "“%s” is not a valid link, links cannot redirect to the current page."
msgstr "「%s」不是有效連結，連結未能重新導向至目前頁面。"
