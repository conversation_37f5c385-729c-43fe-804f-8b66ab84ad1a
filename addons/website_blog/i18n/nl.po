# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' paginakop."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. Resultaten weergeven voor '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Alle datums"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Verrekijkers zijn lichtgewicht en draagbaar.</b> Maar niet als je de luxe"
" hebt om een observatorium vanaf je dek op te zetten en te bedienen, ga je "
"waarschijnlijk reizen om je bezichtigingen uit te voeren. Een verrekijker "
"gaat veel gemakkelijker met je mee en ze zijn lichter om het land in te "
"dragen en te gebruiken terwijl je daar bent dan een omslachtige set voor het"
" opzetten van een telescoop."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Kies de hersenen van de experts</b>. Als je nog niet actief bent in een "
"astronomische vereniging of club, kunnen de verkopers van de telescoopwinkel"
" je begeleiden naar de actieve verenigingen in je omgeving. Als je eenmaal "
"connecties hebt met mensen die telescopen hebben gekocht, kun je advies "
"krijgen over wat werkt en wat je kunt vermijden, dat meer geldig is dan "
"alles wat je krijgt van een webartikel of een verkoper bij Wal-Mart."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "<b>Publiceer je blog</b> om het zichtbaar te maken voor je bezoekers."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Aanmelden</b>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Probeer voordat je koopt.</b> Dit is nog een voordeel van een aantal "
"excursies met de astronomieclub. Je kunt wat quality hours reserveren met "
"mensen die telescopen kennen en hun rigs hebben opgezet om hun uitrusting te"
" onderzoeken, de belangrijkste technische aspecten te leren en ze uit te "
"proberen voordat je geld in je eigen set-up zinkt."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Schrijf hier je verhaal.</b>Gebruik de bovenste werkbalk om je tekst te "
"stijlen: voeg een afbeelding of tabel toe, stel vet of cursief in, enz. "
"Sleep de bouwblokken op de pagina's en laat ze los voor meer grafische "
"blogs."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Naast de inheemse bevolking is ook de lokale fauna een"
" grote publiekstrekker.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">Het is enorm belangrijk dat je de juiste telescoop "
"hebt.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">Dat “Wow” moment is alles waar astronomie om "
"draait.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Hoe meer recensies je leest, hoe meer je merkt dat ze "
"de neiging hebben zich te clusteren in de uitersten van meningen.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Er is iets tijdloos aan de kosmos.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Je studie van de maan kan, net als alle andere dingen,"
" variëren van eenvoudig tot zeer complex.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Geen labels gedefinieerd</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"Inhoud blog\" "
"title=\"Inhoud blog\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Postdatum\" "
"title=\"Postdatum\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Volgende "
"lezen</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Lees volgende\" title=\"Lees volgende\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"me-1\">Show:</span>"
msgstr "<span class=\"me-1\">Toon:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled ps-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled ps-0\">Blogs:</span>"

#. module: website_blog
#: model_terms:web_tour.tour,rainbow_man_message:website_blog.blog
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Goed gedaan!</b> Je hebt alle stappen van deze tour "
"doorlopen.</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Een geweldige manier om verborgen plaatsen te ontdekken"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Een vakantie naar de Copper Canyon belooft een spannende mix te worden van "
"ontspanning, cultuur, geschiedenis, natuur en wandelen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Een nieuwe post"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Een reiziger kan ervoor kiezen om het gebied te verkennen door rond de kloof"
" te wandelen of zich er in te wagen. Gedetailleerde planning is vereist voor"
" degenen die zich in de diepten van de kloof willen wagen. Er zijn een "
"aantal reisorganisaties die gespecialiseerd zijn in het organiseren van "
"reizen naar de regio. Bezoekers kunnen naar Copper Canyon vliegen met een "
"toeristenvisum, dat 180 dagen geldig is. Reizigers kunnen ook vanaf elke "
"plek in de Verenigde Staten rijden en een visum krijgen bij het Mexicaanse "
"douanestation aan de grens."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Over ons"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Bouw vooral, <b> een relatie op met een gerenommeerde telescoopwinkel</b> "
"die mensen in dienst heeft die hun spullen kennen. Als je je telescoop bij "
"een Wal-Mart of warenhuis koopt, is de kans klein dat je het juiste krijgt."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Bekijk post"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Actief"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Wat toevoegen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Alle"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Alle blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Alle blogs"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Alone in the ocean"
msgstr "Alleen in de oceaan"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "Hoe moeilijk is het opzetten en afbreken volgens die lijnen?"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/website_blog.js:0
msgid "Amazing blog article: %(title)s! Check it live: %(url)s"
msgstr "Geweldig blogartikel: %(title)s! Bekijk het live: %(url)s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Een spannende mix van ontspanning, cultuur, historiek, natuur en wandelen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"En als alles is gezegd en gedaan, <b>wees dan uitgerust</b>. Je zoektocht "
"naar nieuwere en betere telescopen zal levenslang duren. Laat je verslaafd "
"raken aan astronomie en de ervaring zal elk aspect van het leven verrijken. "
"Het wordt een verslaving die je nooit wilt doorbreken."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Een ander uniek kenmerk van Copper Canyon is de aanwezigheid van de "
"Tarahumara Indiase cultuur. Deze semi-nomadische mensen leven in "
"grotwoningen. Hun levensonderhoud hangt voornamelijk af van landbouw en "
"veeteelt."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archive"
msgstr "Archiveren"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archives"
msgstr "Archieven"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Artikel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Artikelen"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Astronomieclubs zijn levendige plaatsen vol deskundige amateurs die hun "
"kennis graag met je delen. Voor de prijs van een cola en snacks gaan ze met "
"je naar de sterren kijken en je overweldigen met trivia en grote kennis."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "Astronomie is “sterrenkijken\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom-feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Auteur"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Naam auteur"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Awesome hotel rooms"
msgstr "Geweldige hotelkamers"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Wees er bewust van dit ding genaamd “astronomie”"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Als je deel gaat uitmaken van de samenleving van toegewijde "
"amateurastronomen, krijg je toegang tot deze georganiseerde inspanningen om "
"nieuwe niveaus te bereiken in ons vermogen om de maan van de aarde te "
"bestuderen. En het geeft je leeftijdsgenoten en vrienden die je passie voor "
"astronomie delen en die hun ervaring en expertisegebieden kunnen delen "
"terwijl je zoekt naar waar je het volgende zou kunnen kijken in de enorme "
"nachtelijke hemel, naar de maan en daarbuiten in je zoektocht naar kennis "
"over het schijnbaar eindeloze universum boven ons."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Deel uitmaken van de community van toegewijde amateur astronomen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Slaapkamerfaciliteiten"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Voordat je tot die grote uitgave gaat, is het misschien een betere volgende "
"stap om met het blote oog te investeren in een goede verrekijker. Er is "
"zelfs een verrekijker die geschikt is om naar de sterren te kijken en die je"
" net zo goed dat extra zicht geeft dat je de wonderen van het universum net "
"iets beter wilt zien. Een goed ontworpen verrekijker geeft je ook veel meer "
"mobiliteit en het vermogen om je “verbeterd zicht” binnen handbereik te "
"houden wanneer dat geweldige uitzicht zich net aan je voordoet."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Voordat je je eerste inkoop maakt…"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Voorbij het oog"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Blognaam"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blog Page"
msgstr "Blogpagina"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog Post"
msgstr "Blogpost"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Blogpost omslag"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
msgid "Blog Post Pages"
msgstr "Blogpostpagina's"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Blogpost titel"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_post_pages
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blog Posts"
msgstr "Blogposts"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Blog subtitel"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Bloglabel"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Blog label categorie"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Bloglabels"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Titel blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Titel blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blogs"
msgstr "Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs List"
msgstr "Lijst met blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs Page"
msgstr "Blogpagina"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Bottom"
msgstr "Onder"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Breadcrumb"
msgstr "Kruimelpad"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Maar hoe doorzoek je de geweldige keuzes die worden aangeboden? En nog "
"belangrijker: vertrouw je echt op de foto's en beschrijvingen van de hotels "
"die ze zichzelf hebben toegekend als motivatie om boekingen te krijgen? "
"Beoordelingen van reizigers kunnen nuttig zijn, maar je moet voorzichtig "
"zijn. Ze zijn vaak bevooroordeeld, soms verouderd, en dienen misschien "
"helemaal niet je belangen. Hoe weet je dat de functies die belangrijk zijn "
"voor de recensent, belangrijk voor je zijn?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Een telescoop kopen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Het kopen van de juiste telescoop om je liefde voor astronomie naar een "
"hoger niveau te tillen, is een grote volgende stap in de ontwikkeling van je"
" passie voor de sterren."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Kan publiceren"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cards"
msgstr "Kaarten"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Categorie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Voorzieningen voor kinderen"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Choose an image from the library."
msgstr "Kies een afbeelding uit de bibliotheek."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Click here to add new content to your website."
msgstr "Klik hier om nieuwe inhoud aan je website toe te voegen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Klik op \"<b>Nieuw</b>\" in de rechterbovenhoek om je eerste blogpost te "
"schrijven."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Afsluiten"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__color
msgid "Color"
msgstr "Kleur"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Opmerking"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments"
msgstr "Opmerkingen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments/Views Stats"
msgstr "Statistieken voor opmerkingen/weergaven"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Inhoud"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Copper Canyon is een van de zes kloven in de omgeving. Hoewel de naam "
"suggereert dat de kloof enige relevantie kan hebben voor kopermijnbouw, is "
"dit niet het geval. De naam is afgeleid van het koper en groene korstmos dat"
" de kloof bedekt. Copper Canyon heeft twee klimaatzones. De regio heeft een "
"alpien klimaat aan de top en een subtropisch klimaat op de lagere niveaus. "
"De winters zijn koud met regelmatig sneeuwstormen op grotere hoogte. De "
"zomers zijn droog en heet. De hoofdstad, Chihuahua, is een woestijn op grote"
" hoogte waar het weer varieert van koude winters tot hete zomers. De regio "
"is uniek vanwege de verschillende ecosystemen die erin bestaan."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cover"
msgstr "Omslag"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Cover eigenschappen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Date"
msgstr "Datum"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Datum (nieuw naar oud)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Datum (oud naar nieuw)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Omschrijving"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Oost Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Helikoptervluchten in Oost-Maui geven je uitzicht op de tienduizend meter "
"hoge vulkaan, Haleakala of het huis van de zon. Deze vulkaan is inactief en "
"is voor het laatst uitgebarsten in 1790. Je zult de krater van de vulkaan en"
" de droge, dorre aarde rondom de zuidkant van de vulkaanafgrond kunnen zien "
"met Maui helikoptervluchten."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Bewerken in backend"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Wijzig de '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Wijzig de paginakop voor 'Alle blogs'."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Wijzig de 'Filter resultaten' paginakop."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Edit your title, the subtitle is optional."
msgstr "Bewerk je titel, de ondertitel is optioneel."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Enter your post's title"
msgstr "Voer de titel van je bericht in"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Feiten waarmee je rekening moet houden."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Ten slotte, en het belangrijkste, had het inspectieteam van de "
"kwaliteitshotels van hotels het hotel in kwestie regelmatig moeten bezoeken,"
" het personeel moeten ontmoeten, in een slaapkamer moeten slapen en het eten"
" moeten proberen. Ze moeten het hotel ervaren zoals alleen een hotelgast dat"
" kan en pas dan zijn ze echt in een sterke positie om over het hotel te "
"schrijven."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Follow Us"
msgstr "Volg ons"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Partners)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Voor velen van ons die stadsbewoners zijn, merken we die lucht daar niet "
"routinematig. De lichten van de stad verdoezelen goed de verbazingwekkende "
"vertoning die altijd boven ons hoofd hangt."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Voor velen van ons begint onze allereerste ervaring met het leren over de "
"hemellichamen toen we onze eerste volle maan aan de hemel zagen. Het is "
"werkelijk een schitterend uitzicht, zelfs met het blote oog."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Van de allerkleinste baby tot de meest geavanceerde astrofysicus, er is iets"
" voor iedereen die van astronomie wil genieten. In feite is het een "
"wetenschap die zo toegankelijk is dat vrijwel iedereen het vrijwel overal "
"kan doen. Het enige wat ze moeten weten, is opzoeken."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Full-Width"
msgstr "Volledige breedte"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Zoek een nerd"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Koop een telescoop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Krijg wat historiek"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Ga van start"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Grid"
msgstr "Matrix"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Groeperen op"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Hier zijn de belangrijkste feiten waarmee je rekening moet houden:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Vakantietips"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Hover Effect"
msgstr "Hover-effect"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Hoe op te zoeken"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Hoe complex is de telescoop en heb je moeite met onderhoud? Netwerk om de "
"antwoorden op deze en andere vragen te krijgen. Als je je huiswerk op deze "
"manier doet, zul je precies de juiste telescoop vinden voor deze volgende "
"grote stap in de evolutie van je passie voor astronomie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Hoe mobiel moet je telescoop zijn?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "How to choose the right hotel"
msgstr "Hoe het juiste hotel te kiezen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Als het uitmaakt dat je hotel bijvoorbeeld aan het strand ligt, dichtbij het"
" attractiepark of gunstig gelegen is ten opzichte van de luchthaven, dan "
"staat de locatie voorop. Elke fatsoenlijke directory zou een locatiekaart "
"van het hotel en zijn omgeving moeten bevatten. Er moeten afstandskaarten "
"tot de luchthaven worden aangeboden, evenals een vorm van interactieve "
"kaart."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Als de nacht helder is, kun je verbazingwekkende details van het maanoppervlak zien terwijl je gewoon naar de sterren staart in je achtertuin.\n"
"Natuurlijk zul je naarmate je liefde voor astronomie groeit, veel hemellichamen fascinerend vinden. Maar de maan kan altijd onze eerste liefde zijn, want het is het enige verre ruimtevoorwerp dat de unieke onderscheiding heeft om dicht bij de aarde te vliegen en waarop de mens heeft gelopen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
msgid "In"
msgstr "In"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"In veel opzichten is het een grote stap van iemand die gewoon met astronomie"
" aan het rommelen is naar een serieuze student van de wetenschap. Maar jij "
"en ik weten allebei dat er nog een grote stap is na het kopen van een "
"telescoop voordat je echt weet hoe je hem moet gebruiken."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Increase Readability"
msgstr "Vergroot de leesbaarheid"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is Published"
msgstr "Is gepubliceerd"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Islands"
msgstr "Eilanden"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Het is heel leuk om de sterrenbeelden te leren kennen, door de nachtelijke "
"hemel te navigeren en de planeten en de beroemde sterren te vinden. Er zijn "
"websites en boeken in overvloed om je te begeleiden."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Het is belangrijk om een hotel te kiezen waar je je op je gemak voelt – "
"modern of traditioneel meubilair, lokaal decor of internationaal, formeel of"
" ontspannen. De ideale hotelgids zou je op de hoogte moeten stellen van de "
"beschikbare opties."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Het is veilig om te zeggen dat op een bepaald punt in ons leven, ieder van "
"ons dat moment heeft waarop we plotseling verbluft zijn als we oog in oog "
"komen te staan met de enorme omvang van het universum dat we aan de "
"nachtelijke hemel zien."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"Het is echt verbazingwekkend als je erover nadenkt dat je, door op een "
"willekeurige nacht omhoog te kijken, vrijwel honderdduizenden sterren, "
"sterrenstelsels, planeten, manen, asteroïden, kometen en misschien zelfs af "
"en toe voorbij zou kunnen zien. . Het is zelfs nog adembenemender als je je "
"realiseert dat de lucht waar je naar kijkt in feite exact dezelfde hemel is "
"waar onze voorouders honderden en duizenden jaren geleden van genoten toen "
"ze gewoon opkeken."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Jungle"
msgstr "Jungle"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Weet waar je naar op zoek bent"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Weet wanneer te kijken"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Large"
msgstr "Groot"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Laatste bijdrage"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Laatste blogberichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Layout"
msgstr "Layout"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Door de achtergrond van de grote ontdekkingen in de astronomie te leren, "
"zullen je momenten zinvoller worden om naar de sterren te kijken. Het is een"
" van de oudste wetenschappen op aarde, dus ontdek de groten uit de "
"geschiedenis die vóór je naar deze sterren hebben gekeken."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Recreatieve voorzieningen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "List"
msgstr "Lijst"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"De lokale kleur is geweldig, maar de restaurants en bars van het hotel "
"kunnen een belangrijke rol spelen tijdens je verblijf. Je moet je bewust "
"zijn van keuze, stijl en of ze slim of informeel zijn. Een goed hotelrapport"
" zou je dit moeten vertellen, en dan vooral over de ontbijtfaciliteiten."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Locatie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Maui helicopter tours"
msgstr "Maui helicopter tours"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Maui helikoptervluchten zijn een geweldige manier om het eiland vanuit een "
"ander perspectief te zien en een leuk avontuur te beleven. Als je nog nooit "
"in een helikopter hebt gezeten, is dit een geweldige plek om het te doen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Maui helikoptervluchten zijn een geweldige manier om die plaatsen te "
"verkennen die niet te voet of met de auto bereikbaar zijn. De rondleidingen "
"duren ongeveer een uur en variëren van ongeveer honderdacht vijf dollar tot "
"tweehonderd veertig dollar per persoon. Voor velen is dit een once in a "
"lifetime kans om natuurlijke landschappen te zien die niet meer beschikbaar "
"zullen zijn. Door camera's en video's te nemen om de momenten vast te "
"leggen, kun je de tour keer op keer opnieuw beleven terwijl je door de jaren"
" heen herinneringen ophaalt."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Met helikoptervluchten in Maui kun je al deze bezienswaardigheden zien. Zorg"
" ervoor dat je een camera of video meeneemt wanneer je op Maui "
"helikoptervluchten gaat om de schoonheid van het landschap vast te leggen en"
" om vrienden en familie thuis alle prachtige dingen te laten zien die je "
"tijdens je vakantie hebt gezien."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Medium"
msgstr "Medium"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Meta-omschrijving"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Meta trefwoorden"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Meta titel"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Molokai Maui helikoptervluchten brengen je naar een ander eiland, maar een eiland dat slechts 14 mijl verderop ligt en gemakkelijk bereikbaar is per vliegtuig. Dit eiland heeft een zeer kleine bevolking met een andere cultuur en landschap. De hele kust van het noordoosten is bekleed met kliffen en afgelegen stranden. Ze zijn volledig ontoegankelijk met enig ander vervoermiddel dan door de lucht.\n"
"Mensen die op het eiland wonen, hebben dit opmerkelijke landschap nog nooit gezien, maar niet als ze Maui-helikoptervluchten hebben gemaakt om het te bekijken. Als het regenachtig weer is en er in het seizoen veel regen valt, zie je veel verbazingwekkende watervallen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Belangrijker voor de gezinsreiziger dan de zakenreiziger, je moet uit de "
"gids te weten komen hoe kindvriendelijk het hotel is en van daaruit je "
"beslissing nemen. Een ding dat de moeite waard is, is of het hotel een "
"babysitterservice biedt. Voor de zakenreiziger die aan kinderen wil "
"ontsnappen is dit natuurlijk ook heel relevant – misschien is een hotel dat "
"niet kindvriendelijk is iets geschikter!"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Meest bekeken blogberichten"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Naam"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.blog_post_action_add
msgid "New Blog Post"
msgstr "Nieuw blogpost"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Next Article"
msgstr "Volgend artikel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "No Cover"
msgstr "Geen omslag"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Nog geen blogposts."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Aantal keer bekeken"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Geen resultaten voor \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Er zijn geen resultaten gevonden voor '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Nog geen labels gedefinieerd."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Geen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Dit alles belet je niet verder te gaan met je plannen om een geweldig "
"telescoopsysteem samen te stellen. Zorg ervoor dat je kwaliteitsadvies en "
"training krijgt over hoe je je telescoop kunt configureren om aan je "
"behoeften te voldoen. Aan de hand van deze richtlijnen kun je urenlang "
"sterrenkijken naar de fenomenale bezienswaardigheden aan de nachtelijke "
"hemel die zich buiten het blote oog bevinden."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal"
msgstr "Normaal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal picture"
msgstr "Normaal beeld"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Not Published"
msgstr "Niet gepubliceerd"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Als je niet alleen het weer kent, zal je sterrenkijken lonend zijn, maar als"
" je leert wanneer de grote meteorenregen en andere grote astronomische "
"gebeurtenissen zullen plaatsvinden, zal de opwinding van de astronomie voor "
"jou tot leven komen."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Om je maanverering tot het uiterste te tillen, krijg je natuurlijk de meest "
"verbluffende details van het maanoppervlak als je je uitrusting naar een "
"goede startertelescoop tilt. Met elk van deze upgrades zullen je kennis en "
"de diepte en reikwijdte van wat je kunt zien geometrisch verbeteren. Voor "
"veel amateurastronomen kunnen we soms geen genoeg krijgen van wat we kunnen "
"zien op dit ons dichtstbijzijnde ruimtevoorwerp."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"Once you have reviewed the content on mobile, you can switch back to the "
"normal view by clicking here again"
msgstr ""
"Nadat je de inhoud op mobiel heeft bekeken, kun je teruggaan naar de normale"
" weergave door hier nogmaals te klikken"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Andere"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Our Latest Posts"
msgstr "Onze laatste berichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Onze blogs"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Foto gemaakt door Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Foto gemaakt door Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Foto gemaakt door Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Foto gemaakt door Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Foto gemaakt door Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Foto gemaakt door Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Foto gemaakt door Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Foto gemaakt door Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Foto gemaakt door PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Foto gemaakt door SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Foto gemaakt door Teddy Kelley, @teddykelley"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Picture size"
msgstr "Grootte afbeelding"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Posts"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Posts List"
msgstr "Lijst met berichten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Publicatiedatum"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Published"
msgstr "Gepubliceerd"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Gepubliceerd ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Publicatiedatum"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Gepubliceerde post"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Publicatie opties"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Publicatiedatum"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__rating_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__rating_ids
msgid "Ratings"
msgstr "Beoordelingen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Meer lezen <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Restaurants, cafés en bars"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict to a specific website."
msgstr "Beperken tot een specifieke website."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO geoptimaliseerd"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Voorbeeld"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Satellites"
msgstr "Satellieten"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Search for an image. (eg: type \"business\")"
msgstr "Zoek naar een afbeelding. (bijvoorbeeld: typ \"bedrijf\")"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seaside vs mountain side"
msgstr "Kust versus bergzijde"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seeing the world from above"
msgstr "De wereld van bovenaf zien"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
msgid "Select Blog"
msgstr "Selecteer blog"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select the blog you want to add the post to."
msgstr "Selecteer de blog waar je deze post aan wilt toevoegen."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select this menu item to create a new blog post."
msgstr "Selecteer dit menu item om een nieuw blogpost aan te maken."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Comment"
msgstr "Selecteer om commentaar te geven"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Tweet"
msgstr "Selecteer om te tweeten"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "SEO naam"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Splits elk sleutelwoord met een komma"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Set a blog post <b>cover</b>."
msgstr "Stel een <b>omslag</b> voor een blogpost in."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Verschillende trekvogels en inheemse vogels, zoogdieren en reptielen noemen "
"Copper Canyon hun thuis. De voortreffelijke fauna in dit bijna ongerepte "
"land is ook de moeite van het bekijken waard."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Share Links"
msgstr "Links delen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Deel op Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Deel op LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on X"
msgstr "Delen op X"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Deel deze post"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Sidebar"
msgstr "Zijbalk"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Sierra Tarahumara, in de volksmond bekend als Copper Canyon, ligt in Mexico."
" Het gebied is een favoriete bestemming onder mensen die op zoek zijn naar "
"een avontuurlijke vakantie."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Skies"
msgstr "Luchten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller"
msgstr "Kleiner"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller picture"
msgstr "Kleinere afbeelding"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Het is dus van cruciaal belang dat je precies de juiste telescoop krijgt "
"voor waar je je bevindt en wat je voorkeuren voor sterrenkijken zijn. Laten "
"we om te beginnen de drie belangrijkste soorten telescopen bespreken en "
"vervolgens enkele “Telescope 101″ -concepten bespreken om je kansen te "
"vergroten dat je het juiste koopt."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Het kan dus zijn dat we eens per jaar een vakantie naar een kampeerplek of "
"een uitstapje naar het huis van een familielid op het platteland vinden dat "
"we ons buiten bevinden wanneer de spender van de nachtelijke hemel "
"plotseling besluit zijn spectaculaire show op te zetten. Als je dat soort "
"moment hebt gehad waarop je letterlijk buiten adem werd geslagen door de "
"spender die de nachtelijke hemel ons kan laten zien, kun je je "
"waarschijnlijk dat exacte moment herinneren waarop je weinig anders kon "
"zeggen dan “wauw” over wat je zag."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Dus om precies de juiste soort telescoop te selecteren, zijn je "
"doelstellingen bij het gebruik van de telescoop belangrijk. Om echt de "
"sterke en zwakke punten te begrijpen, niet alleen van de lenzen en het "
"ontwerp van de telescoop, maar ook van hoe de telescoop presteert in "
"verschillende sterrenkijksituaties, is het het beste om van tevoren wat "
"huiswerk te maken en kennis te maken met de verschillende soorten. Dus "
"voordat je je eerste inkoop doet…"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Dus je gaat naar het buitenland, je hebt een bestemming gekozen en nu moet "
"je een hotel uitkiezen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Iemand bekend in <cite title=\"Bron titel\">bron titel</cite>"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Spotting the fauna"
msgstr "De fauna spotten"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "Start writing here..."
msgstr "Start hier met schrijven..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Stijl"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Ondertitel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Subtitel"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Labelcategorieën"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Labelcategorie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Label categorie formulier"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Labelformulier"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Labellijst"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists!"
msgstr "Labelcategorie bestaat al!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Labelnaam bestaat al!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Labels"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags List"
msgstr "Lijst met labels"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Taking pictures in the dark"
msgstr "Foto's maken in het donker"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Teaser"
msgstr "Teaser"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Teaser & Tags"
msgstr "Teaser & LAbels"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Teaser inhoud"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Tien jaar geleden had je waarschijnlijk je plaatselijke reisagent bezocht en"
" vertrouwd op het persoonlijke advies dat je kreeg van de zogenaamde "
"‘experts’. De 21e-eeuwse manier om je hotel te selecteren en te boeken is "
"natuurlijk via internet, via reissites."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Dat “Wow” -moment is waar astrologie om draait. Voor sommigen wordt dat wow-"
"moment een passie die leidt tot een carrière waarin ze de sterren "
"bestuderen. Voor een paar gelukkigen is dat wow-moment een allesverslindende"
" obsessie die ertoe leidt dat ze naar de sterren reizen in de spaceshuttle "
"of op een van onze vroege ruimtemissies. Maar voor de meesten van ons kan "
"astrologie een tijdverdrijf of een gewone hobby worden. Maar we dragen dat "
"wow-moment met ons mee voor de rest van ons leven en gaan op zoek naar "
"manieren om dieper te kijken en meer te leren over het spectaculaire "
"universum dat we elke nacht in de miljoenen sterren boven ons zien."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "De schoonheid van astronomie is dat iedereen het kan doen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"De beste tijd om de maan te zien is natuurlijk 's nachts, wanneer er weinig "
"bewolking is en het weer geschikt is voor een langdurige studie. Het eerste "
"kwartaal levert het grootste detail van onderzoek op. En laat je niet "
"misleiden, maar het uitwissen van een deel van de maan als het niet in het "
"stadium van de volle maan is. Het fenomeen dat bekend staat als “earthshine”"
" geeft je de mogelijkheid om het verduisterde deel van de maan ook met enig "
"detail te zien, zelfs als de maan slechts op kwart of half wordt "
"weergegeven."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "De beste tijd om de maan te bekijken."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Het blogpost zal zichtbaar zijn op je website voor je bezoekers vanaf deze "
"datum indien het ingesteld is als gepubliceerd."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"De kliffen in deze regio behoren tot de hoogste ter wereld en het is gewoon "
"adembenemend om het water vanaf de hoge toppen te zien stromen. Het korte "
"uitstapje vanuit Maui met Maui helikoptervluchten is de moeite waard om de "
"schoonheid van deze natuurlijke omgeving te zien."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"De volledige URL om toegang tot het document te krijgen via de website."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Het volgende dat we natuurlijk willen hebben, is een goede telescoop. Je "
"hebt misschien wel eens een hobbyist gezien die al goed met zijn studie "
"bezig is die echt cool uitziende telescopen ergens op een heuvel neerzet. "
"Dat prikkelt de amateurastronoom in jou, want dat moet de logische volgende "
"stap zijn in de groei van je hobby. Maar hoe je een goede telescoop koopt, "
"kan ronduit verwarrend en intimiderend zijn."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"De site moet een gedetailleerde analyse bieden van vrijetijdsdiensten in het"
" hotel –  spa, zwembad, fitnessruimte, sauna - evenals details van andere "
"faciliteiten in de buurt, zoals golfbanen. 7. Speciale behoeften: de site "
"van de hotelgids moet de bezoeker informeren over de speciale behoeften en "
"het toegankelijkheidsbeleid van elk hotel. Hoewel dit nogmaals niet voor "
"elke bezoeker geldt, is het voor sommigen absoluut essentieel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Beslissingen over het statief of andere accessoires zullen aanzienlijk "
"veranderen met een telescoop die op je dek zal leven, in plaats van een "
"telescoop die je naar veel afgelegen locaties wilt brengen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"Het uitzicht hiervan is werkelijk adembenemend en een bezienswaardigheid die"
" je niet mag missen. Het is ook zeer leerzaam met de kans om een slapende "
"vulkaan van dichtbij te zien, iets dat je niet elke dag kunt zien. Aan de "
"noord- en zuidkant van de vulkaan zie je echter een ongelooflijk ander "
"uitzicht. Deze kanten zijn weelderig en groen en je zult een aantal "
"prachtige watervallen en prachtige struiken kunnen zien. Tropische "
"regenwouden zijn er aan deze kant van het eiland en het is iets dat op geen "
"enkele andere manier gemakkelijk te bereiken is dan door de lucht."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Dan is er het probleem van de motivatie van de recensent. Hoe meer recensies"
" je leest, hoe meer je merkt dat ze de neiging hebben zich te clusteren in "
"de uitersten van meningen. Aan de ene kant heb je boze recensenten met "
"bijlen om te slijpen; aan de andere kant heb je gasten in verrukking "
"gebracht die onvoorstelbaar overdadig lovend zijn. Het zal je niet verbazen "
"dat hotels soms hun eigen lovende recensies plaatsen, of dat de concurrentie"
" van die concurrent de kans krijgt om de concurrentie te verslaan met "
"slechte recensies. Het is logisch om bij het kiezen van een hotel te "
"bedenken wat echt belangrijk voor jou is. Kies dan een online hotellijst met"
" actuele, onafhankelijke en onpartijdige informatie die er echt toe doet."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Er zijn nog overwegingen waarmee je rekening moet houden voor je "
"uiteindelijke aankoopbeslissing."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Er is iets tijdloos aan de kosmos. Het feit dat de planeten en de maan en de"
" sterren erachter er al eeuwen zijn, doet iets met ons gevoel van onze "
"plaats in het universum. In feite zijn veel van de sterren die we met ons "
"blote oog “zien”, in feite licht dat honderdduizenden jaren geleden van die "
"ster kwam. Dat licht bereikt nu net de aarde. Dus op een heel reële manier "
"is omhoog kijken als tijdreizen."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Deze dingen doen er echt toe en elke fatsoenlijke hotelgids zou je dit soort"
" advies over slaapkamers moeten geven – niet alleen het aantal kamers, wat "
"de gebruikelijke optie is!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Dit blok is niet zichtbaar voor je bezoekers"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "This tag already exists"
msgstr "Dit label bestaat al"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Tiny"
msgstr "Klein"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Titel"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Above Cover"
msgstr "Titel boven omslag"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Inside Cover"
msgstr "Titel binnen omslag"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Door met het blote oog naar de maan te staren en vertrouwd te raken met de "
"maankaart, kun je de zeeën, kraters en andere geografische verschijnselen "
"die anderen al in kaart hebben gebracht, uitkiezen om je studie aangenamer "
"te maken. Maankaarten zijn verkrijgbaar bij elke astronomiewinkel of online "
"en ze zijn de investering meer dan waard."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Om te beginnen te leren hoe we de sterren veel beter kunnen observeren, zijn"
" er enkele basiszaken die we nodig hebben om dieper te kijken, verder dan "
"wat we met het blote oog kunnen zien en om de sterren te bestuderen en ervan"
" te genieten. Het eerste dat je nodig heeft, is helemaal geen apparatuur, "
"maar literatuur. Een goede sterrenkaart toont je de belangrijkste "
"sterrenbeelden, de locatie van de belangrijkste sterren die we gebruiken om "
"door de lucht te navigeren en de planeten die groter lijken dan sterren. En "
"als je aan die kaart wat goed gedaan inleidend materiaal toevoegt over de "
"hobby astronomie, ben je goed op weg."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Om het een tandje hoger te krijgen, kan een goede verrekijker wonderen doen "
"voor het detail dat je op het maanoppervlak ziet. Voor de beste resultaten "
"verkrijg je een goed breed veld in de verrekijkerinstellingen, zodat je het "
"maanlandschap in al zijn schoonheid kunt bewonderen. En omdat het bijna "
"onmogelijk is om de verrekijker stil te houden voor de tijd dat je naar dit "
"magnifieke lichaam in de ruimte wilt staren, wil je misschien een goed "
"statief toevoegen aan je apparatuurarsenaal waar je de verrekijker op kunt "
"bevestigen, zodat jij dat kunt doen. bestudeer de maan comfortabel en met "
"een stabiel uitkijkplatform."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Om het naar een natuurlijk volgend niveau te tillen, wil je misschien "
"profiteren van partnerschappen met andere astronomen of door een van de "
"werkelijk geweldige telescopen te bezoeken die zijn opgezet door "
"professionals die hebben geïnvesteerd in betere technieken om atmosferische "
"interferentie te elimineren om de maan te zien nog beter. Het internet kan "
"je toegang geven tot de Hubble en veel van de enorme telescopen die de hele "
"tijd op de maan gericht zijn. Verder werken veel astronomieclubs aan "
"manieren om meerdere telescopen te combineren, zorgvuldig gesynchroniseerd "
"met computers voor het beste zicht op het maanlandschap."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Top Banner"
msgstr "Topbanner"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Reis"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Niet gepubliceerd ("

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Naamloze post"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Gebruik dit icoon om je blogposts te bekijken op <b>mobiele toestellen</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Gebruikt in:"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Viewpoints"
msgstr "Gezichtspunten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Weergaven"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Zichtbaar in alle blogs hun pagina's"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Zichtbaar op huidige website"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "SCHRIJF HIER OF SLEEP BOUWBLOKKEN"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Website"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Website blogs"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Website snippet filter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Meta-omschrijving website"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta keywords"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Website meta titel"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Website opengraph afbeelding"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "Wat als je de Hubble telescoop kon gebruiken"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Hoewel iedereen op elk moment naar de sterren kan kijken en verliefd kan "
"worden, is het plezier van astronomie het leren hoe je steeds vaardiger en "
"toegeruster kunt worden in sterrenkijken die je elke keer dat je omhoog "
"kijkt steeds meer ziet en begrijpt. Hier zijn enkele stappen die je kunt "
"nemen om de momenten die je aan je astronomische hobby kunt besteden, veel "
"aangenamer te maken."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "With a View"
msgstr "Met uitzicht"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "Schrijf een korte tekst om je blog of bedrijf te omschrijven."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Je moet altijd goed nadenken over het soort faciliteiten dat je vanuit je "
"slaapkamer nodig heeft en het hotel zoeken dat de faciliteiten heeft die je "
"belangrijk vindt. De website van de hotelgids moet ingaan op zaken als: "
"grootte van het bed, internettoegang (de kosten, of er wifi of een vaste "
"breedbandverbinding is), gratis voorzieningen, uitzicht vanuit de kamer en "
"luxe aanbiedingen zoals een kussenmenu of badmenu, keuze uit roken of niet "
"roken kamers etc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Je zult al het moois zien dat Maui te bieden heeft en een geweldige tijd "
"beleven voor het hele gezin. Rondleidingen zijn niet al te duur en duren van"
" vijfenveertig minuten tot meer dan een uur. Je kunt plaatsen zien die "
"doorgaans niet toegankelijk zijn met helikoptervluchten op Maui. Plaatsen "
"die niet te voet of met de auto bereikbaar zijn, kunnen per vliegtuig worden"
" bekeken. Adembenemende bezienswaardigheden wachten op degenen die zin "
"hebben in leuke Maui-helikoptervluchten. Als je een aanzienlijke tijd op het"
" eiland verblijft, kun je overwegen om meerdere Maui-helikoptervluchten te "
"doen."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "avontuur"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "blog. Klik hier om de blog te bekijken:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "kruimelpad"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "in"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "ontdekking"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "gidsen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "is gepubliceerd op het"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hotels"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "in"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "telescopen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "om een reactie achter te laten"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "niet gepubliceerd"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"
msgstr ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Nog geen reacties"
