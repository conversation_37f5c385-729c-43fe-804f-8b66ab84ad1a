<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="crm.model_crm_lead" model="ir.model">
            <field name="website_form_key">create_lead</field>
            <field name="website_form_default_field_id" ref="crm.field_crm_lead__description" />
            <field name="website_form_access">True</field>
            <field name="website_form_label">Create an Opportunity</field>
        </record>

        <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>crm.lead</value>
            <value eval="[
                'contact_name',
                'description',
                'email_from',
                'name',
                'partner_name',
                'phone',
                'team_id',
                'user_id',
                'lead_properties',
            ]"/>
        </function>
    </data>
</odoo>
