# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:50+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"Language: is\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
msgid "<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
msgid "Disable Tours"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "Auðkenni"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Valmynd"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Name"
msgstr "Nafn"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Onboarding tours"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
msgid "Open bugger menu."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Path"
msgstr "Path"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll down to reach the next step."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll up to reach the next step."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Sequence"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Start"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
msgid "Start Tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Start tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Test"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Test tour"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
msgid "Testing tours"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Þjórfé"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.js:0
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "Tours"
