# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# Wil <PERSON>doo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-06 14:58+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_type.py:0
msgid "%s hours available"
msgstr "%s ore disponibili"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Livello del piano di maturazione"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_attendance
msgid "Attendance"
msgstr "Presenza"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__attendance
msgid "Attendances"
msgstr "Presenze"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Available"
msgstr "Disponibile"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__calendar
msgid "Calendar"
msgstr "Calendario"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Days"
msgstr "Giorno/i"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr "Dedurre le ore extra"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "Abbandona"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "Ore extra"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr "Ore extra disponibili"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid "Frequency Hourly Source"
msgstr "Origine frequenza oraria"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Hours"
msgstr "Ora/e"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid ""
"If the source is set to \"Calendar\", the amount of worked hours will be "
"computed based on the Employee's working schedule. Otherwise, the amount of "
"worked hours will be based on Attendance records."
msgstr ""
"Se l'origine è impostata sul \"Calendario\", il numero di ore di lavoro "
"verrà calcolato in base al calendario di lavoro del dipendente. Altrimenti, "
"l'importo di ore di lavoro si baserà sui resoconti delle presenze."

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "Nuova richiesta di assegnazione"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""
"Una volta approvato un congedo di questo tipo, le ore extra di presenza "
"saranno detratte."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"Only an Officer or Administrator is allowed to edit the allocation duration "
"in this status."
msgstr ""
"Solo un responsabile o amministratore può modificare la durata "
"dell'assegnazione in questo stato."

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr "Franchigia per gli straordinari"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr "Richiesta di lavoro straordinario"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "Salva"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_accrual_level_view_form
msgid "Source"
msgstr "Sorgente"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""
"Il dipendente non ha abbastanza ore extra per prolungare questa "
"assegnazione."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""
"Il dipendente non ha abbastanza ore extra per prolungare questo congedo."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to request this leave."
msgstr ""
"Il dipendente non ha abbastanza ore extra per richiedere questo congedo."

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""
"Il dipendente non ha abbastanza ore di straordinario per richiedere questo "
"congedo."

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "Ferie"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Assegnazione ferie"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "Tipologia ferie"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "Lavoro straordinario totale"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "User"
msgstr "Utente"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "You do not have enough extra hours to request this leave"
msgstr "Non hai abbastanza ore extra per richiedere questo congedo"
