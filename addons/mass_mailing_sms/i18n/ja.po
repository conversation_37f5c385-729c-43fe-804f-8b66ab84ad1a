# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing_sms
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid " (including link trackers and opt-out link)"
msgstr "(リンクトラッカーとオプトアウトリンクを含む）"

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid " (including link trackers)"
msgstr " (リンクトラッカーを含む)"

#. module: mass_mailing_sms
#. odoo-javascript
#: code:addons/mass_mailing_sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid " (including opt-out link)"
msgstr " (オプトアウトリンクを含む)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"
msgstr ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr " %(mailing_type)s \"%(mailing_name)s\"の24時間統計"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid ""
"<br/>\n"
"                    <span class=\"text-muted\">\n"
"                        <i class=\"fa fa-phone\"/> Contacts\n"
"                    </span>"
msgstr ""
"<br/>\n"
"                    <span class=\"text-muted\">\n"
"                        <i class=\"fa fa-phone\"/> 連絡先\n"
"                    </span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"canceled_text_sms\" invisible=\"mailing_type != 'sms'\">SMS "
"Text Message have been cancelled and will not be sent.</span>"
msgstr ""
"<span name=\"canceled_text_sms\" invisible=\"mailing_type != "
"'sms'\">SMSテキストメッセージは取消され、送信されません。</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"failed_text_sms\" invisible=\"mailing_type != 'sms'\">SMS Text "
"Messages could not be delivered.</span>"
msgstr ""
"<span name=\"failed_text_sms\" invisible=\"mailing_type != "
"'sms'\">SMSテキストメッセージは送信できませんでした。</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"next_departure_text\" invisible=\"mailing_type != 'sms'\">This "
"SMS marketing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\" invisible=\"mailing_type != "
"'sms'\">このSMSマーケティングは以下のために予定されています:</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"process_text_sms\" invisible=\"mailing_type != 'sms'\">SMS Text"
" Messages are being processed.</span>"
msgstr ""
"<span name=\"process_text_sms\" invisible=\"mailing_type != "
"'sms'\">SMSテキストメッセージの処理中です。</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"scheduled_text_sms\" invisible=\"mailing_type != 'sms'\">SMS "
"Text Messages are in queue and will be sent soon.</span>"
msgstr ""
"<span name=\"scheduled_text_sms\" invisible=\"mailing_type != "
"'sms'\">SMSテキストメッセージは待機中で、まもなく送信されます。</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"sent_sms\" invisible=\"mailing_type != 'sms'\">SMS Text "
"Messages have been sent.</span>"
msgstr ""
"<span name=\"sent_sms\" invisible=\"mailing_type != "
"'sms'\">SMSテキストメッセージが送信されました。</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr "<span widget=\"statinfo\">受信者を開く</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid "<span>Valid SMS Recipients</span>"
msgstr "<span>有効なSMS受信者</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears you don't have enough IAP credits. Click here to buy credits.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                            IAPクレジットが不足しているようです。ここをクリックしてクレジットを購入して下さい。\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears your SMS account is not registered. Click here to set up your account.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                            SMSアカウントが登録されていないようです。ここをクリックしてアカウントを設定して下さい。\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be delivered.</strong>"
msgstr "<strong>このSMSは送信できませんでした。</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be sent.</strong>"
msgstr "<strong>このSMSは送信できませんでした。</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This number appears to be invalid.</strong>"
msgstr "<strong>この番号は無効と表示されています。</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "A/B Test"
msgstr "A/B テスト"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_mailings_sms_count
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_mailings_sms_count
msgid "A/B Test Mailings SMS #"
msgstr "A/BテストメーリングSMS数"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "A/B Test: %s"
msgstr "A/B テスト: %s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Average of Bounced"
msgstr "バウンス平均"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Average of Clicked"
msgstr "クリック平均"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "BOUNCED (%i)"
msgstr "バウンス (%i)"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
msgid ""
"Blacklist through SMS Marketing unsubscribe (mailing ID: %(mailing_id)s - "
"model: %(model)s)"
msgstr "SMSマーケティング登録解除によるブラックリスト(メーリングID: %(mailing_id)s- モデル:%(model)s)"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "ブラックリスト"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "ブラックリストされた電話は携帯"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.phone_blacklist_menu
msgid "Blacklisted Phone Numbers"
msgstr "ブラックリスト電話番号"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "ブラックリストされた電話は固定電話"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Bounced (%)"
msgstr "不達率(%)"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "CLICKED (%i)"
msgstr "クリック済(%i)"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Campaign"
msgstr "キャンペーン"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.menu_email_campaigns
msgid "Campaigns"
msgstr "キャンペーン"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Carriage-return-separated list of phone numbers"
msgstr "キャリッジ・リターンで区切られた電話番号のリスト。"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Clicked (%)"
msgstr "クリック率(%)"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_code
msgid "Code"
msgstr "コード"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid ""
"Come back once some SMS Mailings are sent to check out aggregated results."
msgstr "いくつかのSMSメールが送信されたら、集計結果を確認するために戻っててきて下さい。"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_configuration
msgid "Configuration"
msgstr "設定"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "国はサポートされていません"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "国別登録が必要です"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid "Create a Mailing List"
msgstr "メーリングリストを作成"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid "Create a SMS Marketing Mailing"
msgstr "SMSマーケティングメーリングを作成"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid "Create a mailing contact"
msgstr "メール連絡先を作成"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_uid
msgid "Created by"
msgstr "作成者"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_date
msgid "Created on"
msgstr "作成日"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Date"
msgstr "日付"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Discard"
msgstr "破棄"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__display_name
msgid "Display Name"
msgstr "表示名"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_duplicate
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Duplicate"
msgstr "複製"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/res_users.py:0
msgid "Email Marketing"
msgstr "メールマーケティング"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Exclude Blacklisted Phone"
msgstr "ブラックリスト電話番号を除く"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_expired
msgid "Expired"
msgstr "契約期間終了済"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "故障タイプ"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "サニタイズされた電話番号を保存するためのフィールド。検索や比較のスピードアップに役立ちます。"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"For an Email, Subject your Recipients will see in their inbox.\n"
"                    For an SMS Text Message, internal Title of the Message."
msgstr ""
"Eメールの場合、受信者の受信トレイに表示される件名。\n"
"　　　　　SMSテキストメッセージの場合は、メッセージの内部タイトル。"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_subject
msgid ""
"For an email, the subject your recipients will see in their inbox.\n"
"For an SMS, the internal title of the message."
msgstr ""
"Eメールの場合、受信者の受信トレイに表示される件名。\n"
"　　　　　SMSテキストメッセージの場合は、メッセージの内部タイトル。"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "最高クリック率"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "サニタイズされた電話番号がブラックリストに登録されている場合、その連絡先は、どのリストからも、一括送信のSMSを受信できなくなります。"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid ""
"Immediately send the SMS Mailing instead of queuing up. Use at your own "
"risk."
msgstr "待機せず、即座にSMSメールを送信します。自己責任でご利用下さい。"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_allow_unsubscribe
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mass_sms_allow_unsubscribe
msgid "Include opt-out link"
msgstr "オプトアウトリンクを含める"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ブラックリストに登録された電話番号が携帯電話番号であるかどうかを示します。モデル内に携帯電話フィールドと電話番号フィールドの両方がある場合に、　　　どちらの番号がブラックリストに登録されているかを区別するのに役立ちます。"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ブラックリストに登録されたサニタイズされた電話番号が電話番号であるかどうかを示します。モデル内に携帯電話フィールドと電話フィールドの両方がある場合に、　　　どちらの番号がブラックリストに登録されているかを区別するのに役立ちます。"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "クレジット残高不足"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "Insufficient IAP credits"
msgstr "IAPクレジットの不足"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Insufficient credits"
msgstr "クレジット残高不足"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "無効な宛先"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
msgid "Invalid number %s"
msgstr "無効な番号%s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "SMSをメール送信/SMS追跡モデルにリンク"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.link_tracker_menu
msgid "Link Tracker"
msgstr "リンクトラッカー"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__mailing_id
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Mailing"
msgstr "メール"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_contact
msgid "Mailing Contact"
msgstr "メール連絡先"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_list
msgid "Mailing List"
msgstr "メーリングリスト"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_contact_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_contact_menu_sms
msgid "Mailing List Contacts"
msgstr "メーリングリスト連絡先"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_list_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_list_menu_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_contacts
msgid "Mailing Lists"
msgstr "メーリングリスト"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "メール統計"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_tracker__mailing_trace_id
msgid "Mailing Trace"
msgstr "メール送信追跡"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "メールタイプ"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"contact directory."
msgstr "メーリング連絡先を使えば、マーケティング対象者と連絡先ディレクトリを分けることができます。"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__manual
msgid "Manual"
msgstr "手動"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Marketing"
msgstr "マーケティング"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_id
msgid "Mass Mailing"
msgstr "メール一括配信"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_ids
msgid "Mass SMS"
msgstr "一括SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "番号がありません"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile
msgid "Mobile"
msgstr "モバイル"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_search_sms
msgid "My SMS Marketing"
msgstr "自分のSMSマーケティング"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid ""
"No need to import mailing lists, you can send SMS to contacts saved in other"
" Odoo apps."
msgstr "メーリングリストをインポートする必要はありません。他のOdooアプリに保存されているすべての連絡先にSMSを送ることができます。"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "不可"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "配送されていません"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_number
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Number"
msgstr "番号"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/controllers/main.py:0
msgid "Number %s not found"
msgstr "番号%sが見つかりません"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_count
msgid "Number of Mass SMS"
msgstr "一括SMSの番号"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Number(s)"
msgstr "番号"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"Once you send these text messages, they'll be making a grand entrance in all"
" pockets, creating quite the bzzzzz!"
msgstr "こうしたテキストを送信すると、ポケットに素敵に表示され、大きな話題を呼ぶことができるでしょう。"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "Open Recipient"
msgstr "受信者を開く"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_optout
msgid "Opted Out"
msgstr "オプトアウト"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Options"
msgstr "オプション"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "送信されるSMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "ブラックリストに登録された電話番号"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_mobile_search
msgid "Phone/Mobile"
msgstr "電話/携帯電話"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Please enter your phone number"
msgstr "電話番号を入力して下さい。"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "RECEIVED (%i)"
msgstr "受信済 (%i)"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Recipients"
msgstr "宛先"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_rejected
msgid "Rejected"
msgstr "拒否済"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "Report for %(expected)i %(mailing_type)s Sent"
msgstr "%(expected)i %(mailing_type)s 用レポートが送信されました"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_reporting
msgid "Reporting"
msgstr "レポーティング"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_id
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_mailing__mailing_type__sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__trace_type__sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__body_plaintext
msgid "SMS Body"
msgstr "SMS本文"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_list__contact_count_sms
msgid "SMS Contacts"
msgstr "SMS連絡先"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "SMS Content"
msgstr "SMS内容"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_id_int
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS ID"
msgstr "SMS ID"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/res_users.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_mailing_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_mass_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "SMS Marketing"
msgstr "SMSマーケティング"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_trace_report_action_sms
msgid "SMS Marketing Analysis"
msgstr "SMSマーケティング分析"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "SMS Subscription"
msgstr "SMS サブスクリプション"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_template_id
msgid "SMS Template"
msgstr "SMSテンプレート"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "SMS Text Message"
msgstr "SMSテキストメッセージ"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS Trace"
msgstr "SMSトレース"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "SMS Traces"
msgstr "SMSトレース"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_tracker_ids
msgid "SMS Trackers"
msgstr "SMS追跡"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_sms_winner_selection
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_sms_winner_selection
msgid "SMS Winner Selection"
msgstr "SMS当選者選考"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/sms_composer.py:0
msgid "STOP SMS: %(unsubscribe_url)s"
msgstr "SMSを停止: %(unsubscribe_url)s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid "Sanitized Number"
msgstr "サニタイズされた数"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send"
msgstr "送信"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid "Send Directly"
msgstr "直接送信"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send Now"
msgstr "今すぐ送信"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Send SMS"
msgstr "SMS配信"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "SMS送信ウィザード"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send Test"
msgstr "テスト送信"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a Sample SMS"
msgstr "サンプルSMSを送信"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a sample SMS for testing purpose to the numbers below."
msgstr "テスト用のサンプルSMSを以下の番号に送信。"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_server
msgid "Server Error"
msgstr "サーバーエラー"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_trace_ids
msgid "Statistics"
msgstr "統計"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Status"
msgstr "状態"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_sms_test_action
msgid "Test Mailing"
msgstr "テストメール配信"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_sms_test
msgid "Test SMS Mailing"
msgstr "SMSメーリングテスト"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
msgid "Test SMS could not be sent to %(destination)s: %(state)s"
msgstr "テストSMSは以下に送信されませんでした: %(destination)s: %(state)s"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
msgid "Test SMS successfully sent to %s"
msgstr "テストSMS配信が正常に%s宛に送信されました。"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/utm.py:0
msgid ""
"The UTM medium '%s' cannot be deleted as it is used in some main functional "
"flows, such as the SMS Marketing."
msgstr "UTM medium '%s'はSMSマーケティングなど、いくつかの主要な機能フローで使用されているため、削除することはできません。"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
msgid "The following numbers are not correctly encoded: %s"
msgstr "以下の番号は正常にエンコードされませんでした: %s"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "There was an error when trying to unsubscribe"
msgstr "登録解除中の試行中にエラーが発生しました。"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "この電話番号はSMSマーケティングのブラックリストに登録されています。ブラックリストを解除するにはクリックして下さい。"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_subject
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Title"
msgstr "タイトル"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Total Sent"
msgstr "送付合計"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__trace_type
msgid "Type"
msgstr "タイプ"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTMキャンペーン"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_medium
msgid "UTM Medium"
msgstr "UTM媒体"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "未登録アカウント"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "Unregistered IAP account"
msgstr "未登録のIAPアカウント"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Unregistered account"
msgstr "未登録アカウント"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Unsubscribe me"
msgstr "登録解除"

#. module: mass_mailing_sms
#. odoo-python
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
msgid "Unsupported %s for mass SMS"
msgstr "一括SMS配信用にサポートされていない%s"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Valid SMS Recipients"
msgstr "有効なSMS受信者"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Winner Selection"
msgstr "当選者選考"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid ""
"Write an appealing SMS Text Message, define recipients and track its "
"results."
msgstr "魅力的なSMSを書き、受信者を明確に定め、その結果を追跡しましょう。"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "不正な番号フォーマット"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "e.g. Black Friday SMS coupon"
msgstr "例: ブラックフライデーSMSクーポン"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully blacklisted"
msgstr "正常にブラックリストに掲載されました。"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully removed from"
msgstr "正常に以下から削除されました:"
