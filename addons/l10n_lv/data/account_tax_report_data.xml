<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_lv_vat_main_tax_report" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.lv"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="vat_report_column_code" model="account.report.column">
                <field name="name">Code</field>
                <field name="expression_label">code</field>
                <field name="figure_type">string</field>
            </record>
            <record id="vat_report_column_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="vat_report_line_40" model="account.report.line">
                <field name="name">Value of activities</field>
                <field name="code">LV_40</field>
                <field name="expression_ids">
                    <record id="vat_report_line_40_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">40</field>
                    </record>
                    <record id="vat_report_line_40_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_41.balance + LV_411.balance + LV_42.balance + LV_421.balance + LV_43.balance + LV_482.balance + LV_49.balance + LV_50.balance + LV_51.balance + LV_511.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="vat_report_line_41" model="account.report.line">
                        <field name="name">Transactions subject to VAT at standard rate</field>
                        <field name="code">LV_41</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_41_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">41</field>
                            </record>
                            <record id="vat_report_line_41_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">41</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_411" model="account.report.line">
                        <field name="name">Domestic transactions for which the recipient of the goods or services is liable for tax</field>
                        <field name="code">LV_411</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_411_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">411</field>
                            </record>
                            <record id="vat_report_line_411_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">411</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_42" model="account.report.line">
                        <field name="name">Transactions subject to VAT at 12%</field>
                        <field name="code">LV_42</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_42_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">42</field>
                            </record>
                            <record id="vat_report_line_42_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">42</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_421" model="account.report.line">
                        <field name="name">Transactions subject to VAT at 5%</field>
                        <field name="code">LV_421</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_421_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">421</field>
                            </record>
                            <record id="vat_report_line_421_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">421</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_43" model="account.report.line">
                        <field name="name">Transactions subject to VAT at 0%</field>
                        <field name="code">LV_43</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_43_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">43</field>
                            </record>
                            <record id="vat_report_line_43_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">LV_44.balance + LV_45.balance + LV_451.balance + LV_46.balance + LV_47.balance + LV_48.balance + LV_481.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="vat_report_line_44" model="account.report.line">
                                <field name="name">Transactions carried out in free ports and SEZ</field>
                                <field name="code">LV_44</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_44_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">44</field>
                                    </record>
                                    <record id="vat_report_line_44_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">44</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_45" model="account.report.line">
                                <field name="name">Goods supplied to EU Member States, other than those referred to in Article 42(16) of the Law</field>
                                <field name="code">LV_45</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_45_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">45</field>
                                    </record>
                                    <record id="vat_report_line_45_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">45</field>
                                    </record>
                                    <!-- Not visible, but used for EC Sales Report -->
                                    <record id="vat_report_line_ec_goods_tag" model="account.report.expression">
                                        <field name="label">ec_goods</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">45_G</field>
                                    </record>
                                    <record id="vat_report_line_ec_triangular_tag" model="account.report.expression">
                                        <field name="label">ec_triangular</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">45_T</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_451" model="account.report.line">
                                <field name="name">Goods supplied to EU Member States as referred to in Article 42(16) of the Law</field>
                                <field name="code">LV_451</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_451_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">451</field>
                                    </record>
                                    <record id="vat_report_line_451_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">451</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_46" model="account.report.line">
                                <field name="name">Supplies of non-Community goods in customs warehouses and free zones</field>
                                <field name="code">LV_46</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_46_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">46</field>
                                    </record>
                                    <record id="vat_report_line_46_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">46</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_47" model="account.report.line">
                                <field name="name">New vehicles delivered to EU Member States</field>
                                <field name="code">LV_47</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_47_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">47</field>
                                    </record>
                                    <record id="vat_report_line_47_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">47</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_48" model="account.report.line">
                                <field name="name">For services provided</field>
                                <field name="code">LV_48</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_48_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">48</field>
                                    </record>
                                    <record id="vat_report_line_48_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">48</field>
                                    </record>
                                </field>
                            </record>
                            <record id="vat_report_line_481" model="account.report.line">
                                <field name="name">Exported goods</field>
                                <field name="code">LV_481</field>
                                <field name="expression_ids">
                                    <record id="vat_report_line_481_code" model="account.report.expression">
                                        <field name="label">code</field>
                                        <field name="auditable" eval="False"/>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">481</field>
                                    </record>
                                    <record id="vat_report_line_481_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">481</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_482" model="account.report.line">
                        <field name="name">Transactions in other countries</field>
                        <field name="code">LV_482</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_482_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">482</field>
                            </record>
                            <record id="vat_report_line_482_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">482</field>
                            </record>
                            <!-- Not visible, but used for EC Sales Report -->
                            <record id="vat_report_line_ec_services_tag" model="account.report.expression">
                                <field name="label">ec_services</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">482_S</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_49" model="account.report.line">
                        <field name="name">VAT-exempt transactions</field>
                        <field name="code">LV_49</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_49_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">49</field>
                            </record>
                            <record id="vat_report_line_49_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">49</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_50" model="account.report.line">
                        <field name="name">Goods and services received from EU countries (standard rate)</field>
                        <field name="code">LV_50</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_50_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">50</field>
                            </record>
                            <record id="vat_report_line_50_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">50</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_51" model="account.report.line">
                        <field name="name">Goods received from EU Member States (VAT at 12%)</field>
                        <field name="code">LV_51</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_51_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">51</field>
                            </record>
                            <record id="vat_report_line_51_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">51</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_511" model="account.report.line">
                        <field name="name">Goods received from EU Member States (VAT at 5%)</field>
                        <field name="code">LV_511</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_511_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">511</field>
                            </record>
                            <record id="vat_report_line_511_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">511</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_5x" model="account.report.line">
                <field name="name">Calculated VAT</field>
                <field name="code">LV_5x</field>
                <field name="expression_ids">
                    <record id="vat_report_line_5x_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_52.balance + LV_53.balance + LV_531.balance + LV_54.balance + LV_55.balance + LV_56.balance + LV_561.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="vat_report_line_52" model="account.report.line">
                        <field name="name">VAT at standard rate received on taxable transactions</field>
                        <field name="code">LV_52</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_52_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">52</field>
                            </record>
                            <record id="vat_report_line_52_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">52</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_53" model="account.report.line">
                        <field name="name">VAT at 12% received on taxable transactions</field>
                        <field name="code">LV_53</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_53_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">53</field>
                            </record>
                            <record id="vat_report_line_53_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">53</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_531" model="account.report.line">
                        <field name="name">VAT at 5% received on taxable transactions</field>
                        <field name="code">LV_531</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_531_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">531</field>
                            </record>
                            <record id="vat_report_line_531_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">531</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_54" model="account.report.line">
                        <field name="name">VAT for services received</field>
                        <field name="code">LV_54</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_54_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">54</field>
                            </record>
                            <record id="vat_report_line_54_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">54</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_55" model="account.report.line">
                        <field name="name">VAT at standard rate on goods and services received from EU countries</field>
                        <field name="code">LV_55</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_55_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">55</field>
                            </record>
                            <record id="vat_report_line_55_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">55</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_56" model="account.report.line">
                        <field name="name">VAT at 12% on goods received from EU countries</field>
                        <field name="code">LV_56</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_56_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">56</field>
                            </record>
                            <record id="vat_report_line_56_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">56</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_561" model="account.report.line">
                        <field name="name">VAT at 5% on goods and services received from EU countries</field>
                        <field name="code">LV_561</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_561_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">561</field>
                            </record>
                            <record id="vat_report_line_561_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">561</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_60" model="account.report.line">
                <field name="name">VAT paid for goods and services purchased</field>
                <field name="code">LV_60</field>
                <field name="expression_ids">
                    <record id="vat_report_line_60_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">60</field>
                    </record>
                    <record id="vat_report_line_60_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_61.balance + LV_62.balance + LV_63.balance + LV_64.balance + LV_65.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="vat_report_line_61" model="account.report.line">
                        <field name="name">For imported goods</field>
                        <field name="code">LV_61</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_61_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">61</field>
                            </record>
                            <record id="vat_report_line_61_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">61</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_62" model="account.report.line">
                        <field name="name">For domestic goods and services</field>
                        <field name="code">LV_62</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_62_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">62</field>
                            </record>
                            <record id="vat_report_line_62_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">62</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_63" model="account.report.line">
                        <field name="name">VAT charged in accordance with Article 92(1)(4) of the Law (excluding line 64)</field>
                        <field name="code">LV_63</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_63_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">63</field>
                            </record>
                            <record id="vat_report_line_63_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">63</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_64" model="account.report.line">
                        <field name="name">VAT charged on goods and services received from EU countries</field>
                        <field name="code">LV_64</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_64_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">64</field>
                            </record>
                            <record id="vat_report_line_64_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">64</field>
                            </record>
                        </field>
                    </record>
                    <record id="vat_report_line_65" model="account.report.line">
                        <field name="name">Compensation paid to farmers</field>
                        <field name="code">LV_65</field>
                        <field name="expression_ids">
                            <record id="vat_report_line_65_code" model="account.report.expression">
                                <field name="label">code</field>
                                <field name="auditable" eval="False"/>
                                <field name="engine">aggregation</field>
                                <field name="formula">65</field>
                            </record>
                            <record id="vat_report_line_65_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">65</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_66" model="account.report.line">
                <field name="name">VAT paid not deductible as input tax</field>
                <field name="code">LV_66</field>
                <field name="expression_ids">
                    <record id="vat_report_line_66_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">66</field>
                    </record>
                    <record id="vat_report_line_66_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">66</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_67" model="account.report.line">
                <field name="name">Adjustments - reduction of tax calculated in previous tax periods for payment to the state budget</field>
                <field name="code">LV_67</field>
                <field name="expression_ids">
                    <record id="vat_report_line_67_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">67</field>
                    </record>
                    <record id="vat_report_line_67_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">external</field>
                        <field name="formula">most_recent</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_57" model="account.report.line">
                <field name="name">Adjustments - reduction of input tax deducted in previous tax periods</field>
                <field name="code">LV_57</field>
                <field name="expression_ids">
                    <record id="vat_report_line_57_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">57</field>
                    </record>
                    <record id="vat_report_line_57_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">external</field>
                        <field name="formula">most_recent</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_P" model="account.report.line">
                <field name="name">Total - input tax (P)</field>
                <field name="code">LV_P</field>
                <field name="expression_ids">
                    <record id="vat_report_line_P_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_60.balance + LV_67.balance</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_S" model="account.report.line">
                <field name="name">Total - tax calculated (S)</field>
                <field name="code">LV_S</field>
                <field name="expression_ids">
                    <record id="vat_report_line_S_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_5x.balance + LV_57.balance</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_line_70" model="account.report.line">
                <field name="name">Amount of tax to be refunded from the state budget or amount of tax attributable to the next tax period, if P is greater than S</field>
                <field name="code">LV_70</field>
                <field name="expression_ids">
                    <record id="vat_report_line_70_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">70</field>
                    </record>
                    <record id="vat_report_line_70_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_P.balance - LV_S.balance</field>
                        <field name="subformula">if_above(EUR(0))</field>
                    </record>
                </field>
                <field name="aggregation_formula"></field>
            </record>
            <record id="vat_report_line_80" model="account.report.line">
                <field name="name">Amount of tax payable to the national budget if P is less than S</field>
                <field name="code">LV_80</field>
                <field name="expression_ids">
                    <record id="vat_report_line_80_code" model="account.report.expression">
                        <field name="label">code</field>
                        <field name="auditable" eval="False"/>
                        <field name="engine">aggregation</field>
                        <field name="formula">80</field>
                    </record>
                    <record id="vat_report_line_80_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">LV_S.balance - LV_P.balance</field>
                        <field name="subformula">if_above(EUR(0))</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
