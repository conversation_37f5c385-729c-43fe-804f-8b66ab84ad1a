# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_todo
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"&amp;#128075; <br/>\n"
"    Welcome to the To-do app!"
msgstr ""
"&amp;#128075; <br/>\n"
"    ¡Le damos la bienvenida a la aplicación Actividades pendientes!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Añadir una lista de verificación\n"
"                    (/<span style=\"font-style: italic;\">lista de verificación</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Añadir un separador\n"
"                    (/<span style=\"font-style: italic;\">separador</span>)\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Utilicar \n"
"                    /<span style=\"font-style: italic;\">título</span>\n"
"                    para convertir el texto a título\n"
"                </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Debajo de esta lista, pruebe los\n"
"            <span style=\"font-weight: bolder;\">comandos</span>\n"
"            ,\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font style=\"color: #017E84\">escribiendo</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font style=\"background-color: #017E84; color: white\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Seleccione el texto para\n"
"            <font style=\"background-color: #017E84; color: white\">resaltarlo</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">tacharlo</span>\n"
"            o\n"
"            <span style=\"font-weight: bolder;\">darle</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">estilo</span>\n"
"        </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Access your personal pipeline with your to-dos and assigned tasks by going to the Project app and clicking\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">My Tasks</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        There, your to-dos are listed as\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">private tasks.</font>\n"
"        </span>\n"
"        Any task you create privately will also be included in your to-dos. Essentially, they are interchangeable.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Acceda a su flujo y sus tareas pendientes desde la aplicación de Proyecto y luego haga clic en\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Mis tareas</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Allí aparecerán su actividades pendientes como\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">tareas privadas.</font>\n"
"        </span>\n"
"        Todas las tareas que cree en privado también aparecerán en sus actividades pendientes.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        By default, to-dos are only visible to you. You can share them with other users by adding them as\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">assignees</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Por defecto, las tareas pendientes sólo son visibles para usted. Puede compartirlas con otros usuarios si los añade como\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">personas asignadas</font>.\n"
"        </span>\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Customize the stages from the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Kanban view</font>\n"
"        </span>\n"
"        to reflect your preferred workflow.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Personalice las etapas con la\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">vista de kanban</font>\n"
"        </span>\n"
"        para que coincidan con su flujo de trabajo preferido.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        If you want to assign your to-do to a specific project, open the ⚙️ menu and click\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convert to Task</font>.\n"
"        </span>\n"
"        This action will make it visible to other users.\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Si desea asignar su actividad pendiente a un proyecto específico, abra el menú ⚙️ y haga clic en\n"
"        <span>\n"
"            <font style=\"font-weight: bolder;\" class=\"text-o-color-2\">Convertir en tarea</font>.\n"
"        </span>\n"
"        Esta acción hará que otros usuarios puedan verla.\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Use it to manage your work, take notes on the go, and create tasks based on them.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Úsela para administrar su trabajo, tomar notas al instante y crear tareas.\n"
"    </span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Wherever you are, use the magic keyboard shortcut to add yourself a reminder &amp;#128161;\n"
"    </span>\n"
"    <br/>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Use los atajos del teclado para añadir sus recordatorios &amp;#128161;\n"
"    </span>\n"
"    <br/>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Marque esta casilla para indicar que está "
"hecha</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Haga clic en cualquier parte y solo "
"comience a escribir</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Presione Ctrl+Z/⌘+Z para deshacer cualquier"
" cambio</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private to-do is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">En esta actividad pendiente podrá probar todas las funciones que desee.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">¿Está listo para explorar?</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following</span>"
msgstr "<span style=\"font-size: 14px;\">Intente lo siguiente</span>"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Alt + Shift + T</font>\n"
"            </span>\n"
"            (Windows/Linux)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOs)"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Ctrl + Shift + T</font>\n"
"            </span>\n"
"            (MacOS)"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add To-Do"
msgstr "Añadir actividad pendiente"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/web/activity/activity_menu_patch.js:0
msgid "Add a To-Do"
msgstr "Añadir una actividad pendiente"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Add details about your to-do..."
msgstr "Añada detalles sobre esta actividad pendiente..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Archived"
msgstr "Archivado"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__user_id
msgid "Assigned to"
msgstr "Asignado a"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Assignees"
msgstr "Personas asignadas"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By assigned tags"
msgstr "Por etiquetas asignadas"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "By personal stages"
msgstr "Por etapas personales"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Choose tags from the selected project"
msgstr "Elegir etiquetas desde el proyecto seleccionado"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed"
msgstr "Cerrado"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Closed On"
msgstr "Cerrado el"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_controller.js:0
#: model:ir.actions.act_window,name:project_todo.project_task_action_convert_todo_to_task
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Convert to Task"
msgstr "Convertir en tarea"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Convert to-dos into tasks"
msgstr "Convertir las actividades pendientes en tareas"

#. module: project_todo
#: model:ir.model,name:project_todo.model_mail_activity_todo_create
msgid "Create activity and todo at the same time"
msgstr "Crear actividad y actividad pendiente al mismo tiempo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Create to-dos from anywhere"
msgstr "Cree actividades pendientes desde cualquier lugar"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__create_date
msgid "Created on"
msgstr "Creado el"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_conversion_form
msgid "Discard"
msgstr "Descartar"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__date_deadline
msgid "Due Date"
msgstr "Fecha de vencimiento"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Group By"
msgstr "Agrupar por"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Hey"
msgstr "Hola"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__id
msgid "ID"
msgstr "ID"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid ""
"Keep your work organized by using memos and to-do lists.\n"
"                Your to-do items are private by default, but you can choose to share them with others by adding them as assignees."
msgstr ""
"Mantenga su trabajo organizado mediante el uso de notas y listas de actividades pendientes.\n"
"                Sus actividades pendientes son privadas por defecto, pero puede elegir compartirlas con otros si los añade como personas asignadas."

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Manage your to-dos and assigned tasks from a single place"
msgstr ""
"Gestione sus actividades pendientes y tareas asignadas desde un solo lugar"

#. module: project_todo
#: model_terms:ir.actions.act_window,help:project_todo.project_task_action_todo
msgid "No to-do found. Let's create one!"
msgstr "No se encontraron actividades pendientes. ¡Creemos una!"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__note
msgid "Note"
msgstr "Nota"

#. module: project_todo
#: model:res.groups,name:project_todo.group_onboarding_todo
msgid "Onboarding todo already generated for those users"
msgstr ""
"Ya se generó la incorporación de Actividades pendientes para esos usuarios"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Open"
msgstr "Abierto"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Organize your to-dos however you want"
msgstr "Organice sus actividades pendientes como quiera"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.mail_activity_todo_create_popup
msgid "Reminder to..."
msgstr "Recordatorio para..."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_kanban
msgid "Set Cover Image"
msgstr "Establecer imagen de portada"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "Stage"
msgstr "Etapa"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Starred"
msgstr "Destacado"

#. module: project_todo
#: model:ir.model.fields,field_description:project_todo.field_mail_activity_todo_create__summary
msgid "Summary"
msgstr "Resumen"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Tags"
msgstr "Etiquetas"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
#: model:ir.model,name:project_todo.model_project_task
msgid "Task"
msgstr "Tarea"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/res_users.py:0
msgid "To-Do"
msgstr "Actividades pendientes"

#. module: project_todo
#: model:ir.ui.menu,name:project_todo.menu_todo_todos
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do"
msgstr "Actividades pendientes"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "To-do Title"
msgstr "Título de la actividad pendiente"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "To-do..."
msgstr "Actividad pendiente..."

#. module: project_todo
#: model:ir.actions.act_window,name:project_todo.project_task_action_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_activity
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_tree
msgid "To-dos"
msgstr "Actividades pendientes"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_search
msgid "Todos"
msgstr "Actividades pendientes"

#. module: project_todo
#. odoo-javascript
#: code:addons/project_todo/static/src/views/todo_form/todo_form_control_panel.xml:0
msgid "Toggle chatter"
msgstr "Alternar chatter"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_form
msgid "Type Here..."
msgstr "Escriba aquí..."

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Untitled to-do"
msgstr "Actividad pendiente sin título"

#. module: project_todo
#: model:ir.model,name:project_todo.model_res_users
msgid "User"
msgstr "Usuario"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Using the editor"
msgstr "Usando el editor"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/models/project_task.py:0
msgid "Welcome %s!"
msgstr "¡Le damos la bienvenida, %s!"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "Who has access to what?"
msgstr "¿Quién tiene acceso a qué?"

#. module: project_todo
#. odoo-python
#: code:addons/project_todo/wizard/mail_activity_todo_create.py:0
msgid "Your to-do has been successfully added to your pipeline."
msgstr "Su actividad pendiente se ha añadido con éxito a su flujo."

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "convert-todo"
msgstr "convert-todo"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.project_task_view_todo_quick_create_form
msgid "e.g. Send Invitations"
msgstr "p. ej. enviar invitaciones"

#. module: project_todo
#: model:ir.actions.server,name:project_todo.project_task_preload_action_todo
msgid "menu load To-dos"
msgstr "menú de carga de actividades pendientes"

#. module: project_todo
#: model_terms:ir.ui.view,arch_db:project_todo.todo_user_onboarding
msgid "todo-access"
msgstr "todo-access"
