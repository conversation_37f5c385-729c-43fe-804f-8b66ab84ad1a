# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_dhl
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "'rate': {}, 'ship': {}, 'return': {}"
msgstr "'rate': {}, 'ship': {}, 'return': {}"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__0
msgid "0 - Logistics Services"
msgstr "0 - Логистические услуги"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__1
msgid "1 - Domestic Express 12:00"
msgstr "1 - Внутренний экспресс 12:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__2
msgid "2 - B2C"
msgstr "2 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__3
msgid "3 - B2C"
msgstr "3 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__4
msgid "4 - Jetline"
msgstr "4 - Jetline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__5
msgid "5 - Sprintline"
msgstr "5 - Sprintline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__6
msgid "6 - Secureline"
msgstr "6 - Secureline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_a4_pdf
msgid "6X4_A4_PDF"
msgstr "6X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_pdf
msgid "6X4_PDF"
msgstr "6X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_thermal
msgid "6X4_thermal"
msgstr "6X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__7
msgid "7 - Express Easy"
msgstr "7 - Express Easy"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__8
msgid "8 - Express Easy"
msgstr "8 - Express Easy"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_pdf
msgid "8X4_A4_PDF"
msgstr "8X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_tc_pdf
msgid "8X4_A4_TC_PDF"
msgstr "8X4_A4_TC_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_pdf
msgid "8X4_CI_PDF"
msgstr "8X4_CI_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_thermal
msgid "8X4_CI_thermal"
msgstr "8X4_CI_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_pdf
msgid "8X4_PDF"
msgstr "8X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ru_a4_pdf
msgid "8X4_RU_A4_PDF"
msgstr "8X4_RU_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_thermal
msgid "8X4_thermal"
msgstr "8X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__9
msgid "9 - Europack"
msgstr "9 - Europack"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__a
msgid "A - Auto Reversals"
msgstr "A - Автоматические реверсы"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__am
msgid "America"
msgstr "Америка"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__ap
msgid "Asia Pacific"
msgstr "Страны Азиатско-Тихоокеанского региона"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__b
msgid "B - Break Bulk Express"
msgstr "B - Break Bulk Express"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__c
msgid "C - Medical Express"
msgstr "C - Медицинский экспресс"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Перевозчик"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__c
msgid "Centimeters"
msgstr "Сантиметры"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr "Проверьте это, если ваша посылка облагается пошлиной."

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Custom Data"
msgstr "Пользовательские данные"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_custom_data_request
msgid "Custom data for DHL requests,"
msgstr "Индивидуальные данные для запросов DHL,"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__d
msgid "D - Express Worldwide"
msgstr "D - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__delivery_type__dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__stock_package_type__package_carrier_type__dhl
msgid "DHL"
msgstr "DHL"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_account_number
msgid "DHL Account Number"
msgstr "Номер счета DHL"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr "Конфигурация DHL"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "DHL Documents"
msgstr "Документы DHL"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_eu_dom
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_dom_product_template
msgid "DHL EU"
msgstr "DHL ЕС"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_eu_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_intl_product_template
msgid "DHL EU -> International"
msgstr "DHL EU -> International"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_default_package_type_id
msgid "DHL Package Type"
msgstr "DHL Тип пакета"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_password
msgid "DHL Password"
msgstr "DHL Пароль"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_product_code
msgid "DHL Product"
msgstr "Продукт DHL"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_stock
msgid "DHL Shipping Methods"
msgstr "Способы доставки DHL"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr ""
"Отсутствует ID сайта DHL, пожалуйста, измените настройки метода доставки."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_SiteID
msgid "DHL SiteID"
msgstr "Идентификатор сайта DHL"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_us_dom
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_dom_product_template
msgid "DHL US"
msgstr "DHL США"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_us_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_intl_product_template
msgid "DHL US -> International"
msgstr "DHL США -> Международный"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""
"Номер учетной записи DHL отсутствует, пожалуйста, измените настройки метода "
"доставки."

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL doesn't support products with name greater than 75 characters."
msgstr "DHL не поддерживает товары с названием более 75 символов."

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL password is missing, please modify your delivery method settings."
msgstr ""
"Пароль DHL отсутствует, пожалуйста, измените настройки метода доставки."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_duty_payment
msgid "Dhl Duty Payment"
msgstr "Оплата пошлины Dhl"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Dutiable Material"
msgstr "Облагаемый пошлиной материал"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Duties paid by"
msgstr "Оплата пошлин за счет"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__e
msgid "E - Express 9:00"
msgstr "E - Экспресс 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__eu
msgid "Europe"
msgstr "Европа"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__f
msgid "F - Freight Worldwide"
msgstr "F - Всемирный фрахт"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__g
msgid "G - Domestic Economy Select"
msgstr "G - Внутренняя экономика Выберите"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__h
msgid "H - Economy Select"
msgstr "H - экономичный выбор"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Hint: The destination may not require the dutiable option."
msgstr ""
"Подсказка: Пункт назначения может не требовать наличия опции облагаемой "
"пошлиной."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__i
msgid "I - Break Bulk Economy"
msgstr "I - Экономия на сыпучих материалах"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__i
msgid "Inches"
msgstr "Дюймы"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Invalid syntax for DHL custom data."
msgstr "Неверный синтаксис для пользовательских данных DHL."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__j
msgid "J - Jumbo Box"
msgstr "J - Jumbo Box"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__k
msgid "K - Express 9:00"
msgstr "K - Экспресс 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__k
msgid "Kilograms"
msgstr "Килограммы"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__l
msgid "L - Express 10:30"
msgstr "L - Экспресс 10:30"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Label Format"
msgstr "Формат этикетки"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_image_format
msgid "Label Image Format"
msgstr "Формат изображения метки"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_template
msgid "Label Template"
msgstr "Шаблон метки"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__m
msgid "M - Express 10:30"
msgstr "М - Экспресс 10:30"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__n
msgid "N - Domestic Express"
msgstr "N - Внутренний экспресс"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__o
msgid "O - DOM Express 10:30"
msgstr "O - DOM Express 10:30"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Options"
msgstr "Опции"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__p
msgid "P - Express Worldwide"
msgstr "P - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr "Размер блока упаковки"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr "Единица веса упаковки"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"Please define an incoterm in the associated sale order or set a default "
"incoterm for the company in the accounting's settings."
msgstr ""
"Пожалуйста, укажите инкотерм в соответствующем заказе на продажу или "
"установите инкотерм по умолчанию для компании в настройках учета."

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "Please provide at least one item to ship."
msgstr "Пожалуйста, укажите хотя бы один предмет для отправки."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__l
msgid "Pounds"
msgstr "Фунты стерлингов"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Провайдер"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__q
msgid "Q - Medical Express"
msgstr "Q - Медицинский экспресс"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__r
msgid "R - GlobalMail Business"
msgstr "R - GlobalMail Business"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__r
msgid "Recipient"
msgstr "Получатель"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_region_code
msgid "Region"
msgstr "Регион"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__s
msgid "S - Same Day"
msgstr "S - в тот же день"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__s
msgid "Sender"
msgstr "Отправитель"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Shipment created into DHL <br/> <b>Tracking Number: </b>%s"
msgstr "Отправление создано в DHL <br/> <b>Номер отслеживания </b>:%s"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_stock_package_type
msgid "Stock package type"
msgstr "Тип комплекта поставки"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__t
msgid "T - Express 12:00"
msgstr "T - Экспресс 12:00"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"Адрес клиента отсутствует или неверен (отсутствующее поле (поля) :\n"
" %s)"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"Адрес склада вашей компании отсутствует или неправильный (пропущенные поле(я) :\n"
" %s)"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_custom_data_request
msgid ""
"The custom data in DHL is organized like the inside of a json file.\n"
"        There are 3 possible keys: 'rate', 'ship', 'return', to which you can add your custom data.\n"
"        More info on https://xmlportal.dhl.com/"
msgstr ""
"Пользовательские данные в DHL организованы как внутренняя часть json-файла.\n"
"        Существует 3 возможных ключа: \"тариф\", \"отправка\", \"возврат\", к которым вы можете добавить свои пользовательские данные.\n"
"        Дополнительная информация на https://xmlportal.dhl.com/"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Расчетная цена доставки не может быть рассчитана, поскольку отсутствует вес для следующего товара (товаров):\n"
" %s"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid ""
"There is no price available for this shipping, you should rather try with "
"the DHL product %s"
msgstr ""
"На эту доставку нет цены, лучше попробуйте воспользоваться продуктом DHL %s"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__u
msgid "U - Express Worldwide"
msgstr "U - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__v
msgid "V - Europack"
msgstr "V - Europack"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__w
msgid "W - Economy Select"
msgstr "W - экономичный выбор"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__x
msgid "X - Express Envelope"
msgstr "X - экспресс-конверт"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__y
msgid "Y - Express 12:00"
msgstr "Д - Экспресс 12:00"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "You can't cancel DHL shipping without pickup date."
msgstr "Вы не можете отменить доставку DHL без даты получения."

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "You cannot delete the commercial invoice sequence."
msgstr "Вы не можете удалить последовательность коммерческих счетов-фактур."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__z
msgid "Z - Destination Charges"
msgstr "Z - Плата за место назначения"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__zpl2
msgid "ZPL2"
msgstr "ZPL2"
