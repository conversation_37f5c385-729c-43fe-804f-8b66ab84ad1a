# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_payroll
# 
# Translators:
# yael terner, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> Nah<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: documents_hr_payroll
#: model:mail.template,body_html:documents_hr_payroll.mail_template_new_declaration
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, a new declaration file is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__document_id
msgid "Document"
msgstr "מסמך"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Document posting is not properly set in configuration"
msgstr ""

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Documents"
msgstr "מסמכים"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_count
msgid "Documents Count"
msgstr "כמות מסמכים"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_declaration_mixin__documents_enabled
msgid "Documents Enabled"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_hr_payslips_tags
msgid "Documents Hr Payslips Tags"
msgstr "מסמכי משאבי אנוש תגיות תלושי שכר"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_payroll_folder_id
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_payroll_folder_id
msgid "Documents Payroll Folder"
msgstr "תיקיית תלושי שכר"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "חוזה עובד"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDFs are gonna be posted in Documents shortly"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "תלוש שכר"

#. module: documents_hr_payroll
#. odoo-python
#: code:addons/documents_hr_payroll/models/res_company.py:0
msgid "Payroll"
msgstr "שכר"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr ""

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.res_config_settings_view_form
msgid "Payroll Workspace"
msgstr "איזור עבודה לחשבי שכר"

#. module: documents_hr_payroll
#: model:mail.template,name:documents_hr_payroll.mail_template_new_declaration
msgid "Payroll: New Declaration"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_hr_payslips_tags
msgid "Payslip"
msgstr "תלוש שכר"

#. module: documents_hr_payroll
#: model:documents.tag,name:documents_hr_payroll.documents_tag_payslips
msgid "Payslips (HR)"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__pdf_to_post
msgid "Pdf To Post"
msgstr ""

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Post PDFs"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_posted
msgid "Posted PDF"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields.selection,name:documents_hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_post
msgid "Queued PDF posting"
msgstr ""

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_hr_payroll_employee_declaration__state
msgid "State"
msgstr "סטטוס"

#. module: documents_hr_payroll
#: model:mail.template,subject:documents_hr_payroll.mail_template_new_declaration
msgid "{{ object.name }}, a new declaration file is available for you"
msgstr ""
