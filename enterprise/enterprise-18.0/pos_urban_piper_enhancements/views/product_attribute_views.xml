<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_template_attribute_value_view_tree_pos_urban_piper_enhancements" model="ir.ui.view">
        <field name="name">product.template.attribute.value.view.list.pos.urban.piper.enhancements</field>
        <field name="model">product.template.attribute.value</field>
        <field name="inherit_id" ref="product.product_template_attribute_value_view_tree"/>
        <field name="arch" type="xml">
            <field name="image" position="after">
                <field name="urbanpiper_pos_config_ids" widget="many2many_tags" optional="show"/>
            </field>
        </field>
    </record>

    <record id="product_template_attribute_value_view_form_pos_urban_piper_enhancements" model="ir.ui.view">
        <field name="name">product.template.attribute.value.view.form.pos.urban.piper.enhancements</field>
        <field name="model">product.template.attribute.value</field>
        <field name="inherit_id" ref="product.product_template_attribute_value_view_form"/>
        <field name="arch" type="xml">
            <field name="image" position="after">
                <field name="urbanpiper_pos_config_ids" widget="many2many_tags"/>
            </field>
        </field>
    </record>
</odoo>
