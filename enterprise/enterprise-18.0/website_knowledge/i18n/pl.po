# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_knowledge
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article not Published"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Article not found"
msgstr "Nie znaleziono artykułu"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article shared to web"
msgstr ""

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__can_publish
msgid "Can Publish"
msgstr "Można publikować"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__is_published
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_tree
msgid "Is Published"
msgstr "Opublikowane"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "It and its published children can be read by anyone"
msgstr ""

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Artykuł Wiedzy"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.articles_template
msgid "Load more"
msgstr "Wczytaj więcej"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "Log in"
msgstr "Zaloguj"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.public_sidebar
msgid "No article found"
msgstr "Nie znaleziono artykułu"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Only specific people can access"
msgstr ""

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_publish_articles
msgid "Publish Articles"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Publish this Article and its children on the web"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Search an article..."
msgstr "Przeszukaj artykuł"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Share to web"
msgstr "Udostępnij w sieci"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Sign in"
msgstr "Zaloguj się"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__summary
msgid "Summary"
msgstr "Podsumowanie"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid ""
"The article you are trying the read has either been removed or you do not "
"have access to it."
msgstr ""
"Artykuł, który próbujesz przeczytać, został usunięty lub nie masz do niego "
"dostępu."

#. module: website_knowledge
#: model:ir.model.fields,help:website_knowledge.field_knowledge_article__website_url
msgid "The full URL to access the document through the website."
msgstr "Pełny adres URL dostępu do dokumentu przez stronę."

#. module: website_knowledge
#. odoo-python
#: code:addons/website_knowledge/controllers/main.py:0
msgid ""
"This Article cannot be unfolded. Either you lost access to it or it has been"
" deleted."
msgstr ""
"Tego artykułu nie można rozwinąć. Albo utraciłeś do niego dostęp, albo "
"został usunięty."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "This view is only available for internal users"
msgstr "Widok jest dostępny tylko dla użytkowników wewnętrznych"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Toggle aside menu"
msgstr "Przełącz menu boczne"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_unpublish_articles
msgid "Unpublish Articles"
msgstr ""

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Untitled"
msgstr "Bez tytułu"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_published
msgid "Visible on current website"
msgstr "Widoczne na obecnej stronie"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_website
msgid "Website"
msgstr "Strona internetowa"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_url
msgid "Website URL"
msgstr "Adres strony internetowej"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "loader"
msgstr "ładowarka"
