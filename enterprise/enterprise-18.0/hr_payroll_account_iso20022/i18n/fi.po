# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_iso20022
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Konsta Aavaranta, 2024
# <PERSON><PERSON>ra <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">Untrusted</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">Trusted</span>\n"
"                        </span>"
msgstr ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">Ei-luotettu</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">Luotettu</span>\n"
"                        </span>"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Bank Account"
msgstr "Tilinumero"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__journal_id
msgid "Bank Journal"
msgstr "Pankkipäiväkirja"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "Luo maksuraportti"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_employee
msgid "Employee"
msgstr "Työntekijä"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_employee_invalid_bank_account
msgid "Employees With Invalid Bank Accounts"
msgstr "Työntekijät, joiden pankkitilit eivät ole voimassa"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_employee_no_country
msgid "Employees Without Any Country"
msgstr "Työntekijät ilman mitään maata"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_untrusted_bank_accounts
msgid "Employees with untrusted Bank Account numbers"
msgstr "Työntekijät, joiden pankkitilin numerot eivät ole luotettavia"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "Vientimuoto"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "Ohjattu HR-palkanmaksuraportti"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Mark as paid"
msgstr "Merkitse maksetuksi"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip
msgid "Pay Slip"
msgstr "Palkkalaskelma"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Palkkalaskelman erät"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields.selection,name:hr_payroll_account_iso20022.selection__hr_payroll_payment_report_wizard__export_format__sepa
msgid "SEPA"
msgstr "SEPA"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid "Send Money"
msgstr "Tee tilisiirto"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid ""
"Sending fake invoices with a fraudulent account number is a common phishing "
"practice. To protect yourself, always verify new bank account numbers, "
"preferably by calling the vendor, as phishing usually happens when their "
"emails are compromised. Once verified, you can activate the ability to send "
"money."
msgstr ""
"Väärennettyjen laskujen lähettäminen väärennetyllä tilinumerolla on yleinen "
"phishing-käytäntö. Suojautuaksesi tarkista aina uudet pankkitilinumerot, "
"mieluiten soittamalla myyjälle, sillä phishing tapahtuu yleensä silloin, kun"
" heidän sähköpostinsa ovat vaarassa. Kun olet tarkistanut, voit aktivoida "
"mahdollisuuden lähettää rahaa."

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a valid name on the work contact."
msgstr ""
"Joillakin työntekijöillä (%s) ei ole kelvollista nimeä työyhteystiedoissa."

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a work contact."
msgstr "Joillakin työntekijöillä (%s) ei ole työyhteystietoja."

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid ""
"The following employees have invalid bank accounts and could not be trusted:\n"
"%s"
msgstr ""
"Seuraavilla työntekijöillä on virheelliset pankkitilit, eikä niihin voi luottaa:\n"
"%s"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"Päiväkirja '%s' edellyttää asianmukaista IBAN-tiliä SEPA-maksua varten. "
"Määritä se ensin."

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Trust Bank Account"
msgstr "Luota pankkitiliin"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "UETR"
msgstr "UETR"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr "Yksilöllinen alusta loppuun tapahtumaviite"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Untrust Bank Account"
msgstr "Muuta pankkitili ei-luotetuksi"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid "You do not have the right to trust or un-trust a bank account."
msgstr ""
"Sinulla ei ole oikeutta luottaa pankkitiliin tai olla luottamatta siihen."
