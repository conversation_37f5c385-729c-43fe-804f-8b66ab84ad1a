# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_generator
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_generator
#: model:mail.template,body_html:website_generator.email_template_website_scrapped
msgid ""
"<div>\n"
"            <p><b>Great news!</b> Your website conversion is complete and your new integrated Odoo website is now live.</p>\n"
"            <p>Check out your new website here: <a t-att-href=\"ctx.get('website_url')\"><t t-out=\"ctx.get('website_url')\"/></a></p>\n"
"            <p>Thank you for choosing Odoo for your web integration!</p>\n"
"            <p>Best regards,</p>\n"
"            <br/>\n"
"            <p>Odoo - The best open source website builder.</p>\n"
"            </div>\n"
"            "
msgstr ""
"<div>\n"
"            <p><b>好消息！</b>您的网站已完成转换，已整合的新 Odoo 网站现已上线。</p>\n"
"            <p>您可在这里查看新网站： <a t-att-href=\"ctx.get('website_url')\"><t t-out=\"ctx.get('website_url')\"/></a></p>\n"
"            <p>感谢你选择 Odoo 进行网站整合！</p>\n"
"            <p>谨致问候，</p>\n"
"            <br/>\n"
"            <p>Odoo - 最好的开源网站建立工具。</p>\n"
"            </div>\n"
"            "

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__additional_urls
msgid "Additional URLs"
msgstr "额外网址"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "An error occurred"
msgstr "发生错误"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Attachment not found"
msgstr "找不到附件"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Banned url"
msgstr "禁止的网址"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Could not fetch result, invalid output uuid or result expired"
msgstr "无法获取结果，无效的输出 uid 或结果过期"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__create_uid
msgid "Created by"
msgstr "创建人"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__create_date
msgid "Created on"
msgstr "创建日期"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Done, website generated"
msgstr "完成，网站已创建"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "During beta, the number of imported pages may be limited."
msgstr "在测试期间，导入的页面数量可能会受到限制。"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Enter your website URL"
msgstr "输入您的网站网址"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Features"
msgstr "功能"

#. module: website_generator
#: model:ir.model,name:website_generator.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__id
msgid "ID"
msgstr "ID"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Import Products"
msgstr "导入产品"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Import my website"
msgstr "导入我的网站"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Import your"
msgstr "导入您的"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/generator_wait/generator.js:0
msgid "Importing your website."
msgstr "导入您的网站。"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Invalid dbuuid"
msgstr "无效资料库识别码 dbuuid"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Invalid import products"
msgstr "导入产品无效"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Invalid token"
msgstr "无效令牌"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Invalid url"
msgstr "无效网址"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__notified
msgid "Notified"
msgstr "已通知"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Number of allowed requests exhausted"
msgstr "已耗尽的允许请求数"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Number of concurrent requests exceeded"
msgstr "超过并发请求数"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__page_count
msgid "Number of pages"
msgstr "页数"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__uuid
msgid "Output UUID generated from Website Scraper Server"
msgstr "从网站刮板服务器生成的输出 UUID"

#. module: website_generator
#: model:ir.model,name:website_generator.model_website_page
msgid "Page"
msgstr "页"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Pages"
msgstr "页面"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Processing"
msgstr "处理中"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Request is still processing, result not available yet"
msgstr "请求仍在处理中，结果尚未可用"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Server is currently under maintenance. Please retry later"
msgstr "服务器当前正在维护中。请稍后再试"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/generator_wait/generator.xml:0
msgid "Something went wrong"
msgstr "出了问题"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.js:0
msgid "Something went wrong while importing your website."
msgstr "导入您的网站时出现问题。"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__status
msgid "Status"
msgstr "状态"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__status_message
msgid "Status Message"
msgstr "状态消息"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Success"
msgstr "成功"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/systray_items/generator_request.js:0
msgid "The import of your website has started!"
msgstr "您的网站导入已经开始！"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "The request asks for too many pages"
msgstr "请求请求的页面太多"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/website.py:0
msgid "The website scraper service is currently unavailable."
msgstr "网站抓取服务目前不可用。"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/generator_wait/generator.xml:0
msgid "This can take a few minutes."
msgstr "这可能需要几分钟的时间。"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/page.py:0
msgid "Top Menu for Website %s"
msgstr "网站顶级菜单 %s"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__target_url
msgid "URL to scrape"
msgstr "要抓取的URL"

#. module: website_generator
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__version
msgid "Version"
msgstr "版本"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Version is unsupported"
msgstr "版本不支持"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Waiting for the server to process the request"
msgstr "等待服务器处理请求"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/generator_wait/generator.xml:0
msgid "We could not convert your website, please try again later"
msgstr "我们无法转换您的网站，请稍后再试"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/systray_items/generator_request.js:0
msgid "We will notify %(email)s when everything is ready."
msgstr "一切准备就绪后，我们将通知%(email)s。"

#. module: website_generator
#: model:ir.model,name:website_generator.model_website
#: model:ir.model.fields,field_description:website_generator.field_website_generator_request__website_id
msgid "Website"
msgstr "网站"

#. module: website_generator
#: model:ir.actions.client,name:website_generator.website_generator_screen
msgid "Website Generator"
msgstr "网站生成器"

#. module: website_generator
#: model:ir.actions.server,name:website_generator.action_convert_scraping_request_ICP
msgid "Website Generator After Install"
msgstr "安装后的网站生成工具"

#. module: website_generator
#: model:ir.model,name:website_generator.model_website_generator_request
msgid "Website Generator Request"
msgstr "网站生成工具请求"

#. module: website_generator
#: model:ir.actions.server,name:website_generator.cron_get_result_ir_actions_server
msgid "Website Generator: Get website scraper results"
msgstr "网站生成器: 获得网站刮板结果"

#. module: website_generator
#: model:mail.template,name:website_generator.email_template_website_scrapped
msgid "Website Scraped"
msgstr "网站已抓取"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "Website URL"
msgstr "网址"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Website blocked or unreachable"
msgstr "网站被封锁或未能连接"

#. module: website_generator
#. odoo-python
#: code:addons/website_generator/models/generator.py:0
msgid "Website not supported"
msgstr "网站不被支持"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/generator_wait/generator.xml:0
msgid "You will be redirected once your website is ready."
msgstr "一旦您的网站准备就绪，您将被重定向。"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "You'll be able to create your pages later on."
msgstr "稍后您将能够创建您的网页。"

#. module: website_generator
#: model:mail.template,subject:website_generator.email_template_website_scrapped
msgid "Your Odoo Website is Ready!"
msgstr "您的 Odoo 网站已准备就绪！"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "and"
msgstr "and"

#. module: website_generator
#. odoo-javascript
#: code:addons/website_generator/static/src/client_actions/configurator/configurator.xml:0
msgid "beta"
msgstr "测试"
