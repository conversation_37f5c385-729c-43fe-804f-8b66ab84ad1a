# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>iam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_worked_days.py:0
msgid " (Half-Day)"
msgstr " (ครึ่งวัน)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr "# สลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
msgid "# Payslips"
msgstr "# สลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "%(employee_name)s-declaration-%(year)s"
msgstr "%(employee_name)s-declaration-%(year)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid ""
"%(error_type)s\n"
"- Employee: %(employee)s\n"
"- Contract: %(contract)s\n"
"- Payslip: %(payslip)s\n"
"- Salary rule: %(name)s (%(code)s)\n"
"- Error: %(error_message)s"
msgstr ""
"%(error_type)s\n"
"- พนักงาน: %(employee)s\n"
"- สัญญา: %(contract)s\n"
"- สลิปเงินเดือน: %(payslip)s\n"
"- เกณฑ์เงินเดือน: %(name)s (%(code)s)\n"
"- ข้อผิดพลาด: %(error_message)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "%(rate)s Hours/week"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "%(start_date_string)s and %(end_date_string)s"
msgstr "%(start_date_string)s และ %(end_date_string)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_light_payslip
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.name)"
msgstr "'สลิปเงินเดือน - %s' % (object.name)"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "(%s Payslips)"
msgstr "(%s สลิปเงินเดือน)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When the user cancels a payslip, the status is 'Canceled'."
msgstr ""
"* เมื่อสร้างสลิปเงินเดือนแล้ว สถานะจะเป็น 'ร่าง'\n"
"                \n"
"* หากสลิปเงินเดือนอยู่ระหว่างการตรวจสอบ สถานะจะเป็น 'รอ'\n"
"                \n"
"* หากสลิปเงินเดือนได้รับการยืนยันสถานะจะเป็น 'เสร็จสิ้น'\n"
"                \n"
"* เมื่อผู้ใช้ยกเลิกสลิปเงินเดือน สถานะจะเป็น 'ยกเลิก'"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr "-> รายงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr "/ ชั่วโมง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr "/ เดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ day"
msgstr "/ วัน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-month"
msgstr "/ ครึ่งเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-year"
msgstr "/ ครึ่งปี"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ quarter"
msgstr "/ ไตรมาส"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two months"
msgstr "/ สองเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two weeks"
msgstr "/ สองสัปดาห์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ week"
msgstr "/ สัปดาห์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ year"
msgstr "/ ปี"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "1. Save documents to terminated employees"
msgstr "1. บันทึกเอกสารให้กับพนักงานที่ถูกเลิกจ้าง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "100"
msgstr "100"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr "เงินเดือนที่ 13"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "1st semester of %s"
msgstr "ภาคการศึกษาที่ 1 ของ %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "2. Index salaries for Marketing department"
msgstr "2. ดัชนีเงินเดือนสำหรับฝ่ายการตลาด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "2023 Payroll"
msgstr "บัญชีเงินเดือนปี 2566"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "2nd semester of %s"
msgstr "ภาคการศึกษาที่ 2 ของ %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "3. Create a new contract for Marc Demo with his new position"
msgstr "3. สร้างสัญญาใหม่สำหรับ Marc Demo พร้อมตำแหน่งใหม่ของเขา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Save your Salary Rule in order to add Parameter Values.\n"
"                        </span>"
msgstr ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            บันทึกกฎเงินเดือนของคุณเพื่อเพิ่มค่าพารามิเตอร์\n"
"                        </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ms-2\"> hours/week</span>"
msgstr "<span class=\"ms-2\"> ชั่วโมง/สัปดาห์</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            รายการงาน\n"
"                            </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\"> Payslips </span>\n"
"                            <span class=\"o_stat_value\"> New </span>"
msgstr ""
"<span class=\"o_stat_text\"> สลิปเงินเดือน </span>\n"
"                            <span class=\"o_stat_value\"> สร้าง </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
msgid "<span class=\"o_stat_text\">Employees</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr "<span class=\"o_stat_text\">สลิปเงินเดือน</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\">Salary Attachments</span>\n"
"                            <span class=\"o_stat_value\">New</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span nolabel=\"1\" colspan=\"2\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span nolabel=\"1\" "
"colspan=\"2\">โปรแกรมนี้จะสร้างสลิปเงินเดือนสำหรับพนักงานที่เลือกทั้งหมดตามวันที่และใบลดหนี้ที่ระบุในใช้งานสลิปเงินเดือน</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">You have selected contracts that are not running, this "
"wizard can only index running contracts.</span>"
msgstr ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">คุณได้เลือกสัญญาที่ไม่ได้ทำงานอยู่ "
"ตัวช่วยสร้างนี้สามารถจัดทำดัชนีสัญญาที่ทำงานอยู่เท่านั้น</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr "<span>/ ชั่วโมง</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""
"<span><strong>ทิป:</strong> ทุกครั้งที่คุณแก้ไขปริมาณหรือจำนวนเงินในบรรทัด "
"เราจะคำนวณบรรทัดต่อไปนี้ใหม่ "
"เราขอแนะนำให้คุณแก้ไขจากบนลงล่างเพื่อป้องกันไม่ให้รุ่นของคุณถูกเขียนทับโดยการคำนวณใหม่อัตโนมัติ"
" ระวังว่าการเรียงลำดับบรรทัดใหม่ไม่ได้คำนวณใหม่</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>วัน</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "<span>Payslips</span>"
msgstr "<span>สลิปเงินเดือน</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<span>Total</span>"
msgstr "<span>ทั้งหมด</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "<span>Versions</span>"
msgstr "<span>เวอร์ชั่น</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Address:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Children:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Computed On:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Start Date:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Type:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Department:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Email:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">ID:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Job Position:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Marital Status:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Name:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Pay Period:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Working Schedule:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:web_tour.tour,rainbow_man_message:hr_payroll.payroll_tours
msgid ""
"<strong>Congrats, Your first payslip is now finished. It's time for you to "
"explore the Payroll app by yourself.</strong>"
msgstr ""
"<strong>ขอแสดงความยินดี ตอนนี้สลิปเงินเดือนใบแรกของคุณเสร็จเรียบร้อยแล้ว "
"ถึงเวลาที่คุณจะสำรวจแอปบัญชีเงินเดือนด้วยตัวคุณเอง</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr "<strong>ชื่อลงทะเบียน:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr "<strong>ทั้งหมด</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "<strong>YTD</strong>"
msgstr "<strong>ต้นปี</strong>"

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        เรียน <t t-esc=\"object.employee_id.name\"/>สลิบเงินเดือนใหม่พร้อมแล้ว<br/><br/>\n"
"       โปรดดู PDF ในพอทัลพนักงานของคุณ<br/><br/>\n"
"        ขอให้เป็นวันที่ดี<br/>\n"
"        ทีม HR\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr "การขาดงานที่คาดไม่ถึงจะต้องเป็นการลา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "การบัญชี"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr "จำนวนที่เปิดใช้งาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
#: code:addons/hr_payroll/static/src/views/payslip_batch_form/payslip_batch_form.js:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Add Payslips"
msgstr "เพิ่มสลิปเงินเดือน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a <strong>name</strong> to the contract."
msgstr "เพิ่ม<strong>ชื่อ</strong>ให้กับสัญญา"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a employee to your contract"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr "เพิ่มโครงสร้างเงินเดือนใหม่"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "เพิ่มหมายเหตุภายใน..."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__ytd_computation
msgid ""
"Adds a column in the payslip that shows the accumulated amount paid for "
"different rules during the year"
msgstr ""
"เพิ่มคอลัมน์ในสลิปเงินเดือนที่แสดงยอดเงินสะสมที่ชำระสำหรับกฎเกณฑ์ต่างๆ "
"ในแต่ละปี"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
msgid "All"
msgstr "ทั้งหมด"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr "สลิปเงินเดือนทั้งหมด"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "เงินสงเคราะห์"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "เป็นจริงเสมอ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr "จำนวน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_other_input_id
msgid "Amount Other Input"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr "ชนิดยอดเงิน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Amount to pay"
msgstr "จำนวนเงินที่ต้องชำระ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each payslip."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this payslip, Payslip Amount or less depending on the "
"Remaining Amount."
msgstr ""
"จำนวนเงินที่ต้องชำระสำหรับสลิปเงินเดือนนี้ จำนวนสลิปเงินเดือนหรือน้อยกว่า "
"ขึ้นอยู่กับจำนวนเงินคงเหลือ"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_employee_declaration_unique_employee_sheet
msgid "An employee can only have one declaration per sheet."
msgstr "พนักงานสามารถมีการประกาศได้หนึ่งรายการต่อแผ่นเท่านั้น"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr "ประจำปี"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one?"
msgstr ""
"พบสลิปการคืนเงินอีกอันที่มีจำนวนเงินเท่ากัน คุณต้องการสร้างอันใหม่หรือไม่?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "ปรากฏบนสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"ใช้กฎนี้ในการคำนวณถ้าเงื่อนไขเป็นจริง คุณสามารถระบุเงื่อนไขเช่นพื้นฐาน > "
"1,000"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr "วันที่สิ้นสุดโดยประมาณ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__4
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__4
msgid "April"
msgstr "เมษายน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.js:0
msgid ""
"Are you sure you want to delete this note? All content will be definitely "
"lost."
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบหมายเหตุนี้ เนื้อหาทั้งหมดจะหายไป"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_assignment_of_salary_rule
msgid "Assignment of Salary"
msgstr "การมอบหมายเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"At least one previous negative net could be reported on this payslip for %s"
msgstr ""
"สามารถรายงานผลลบสุทธิก่อนหน้านี้อย่างน้อยหนึ่งรายการในสลิปเงินเดือนนี้สำหรับ"
" %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr "ชื่อที่แนบมาด้วย"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_attachment_of_salary_rule
msgid "Attachment of Salary"
msgstr "เอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__attendance
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__attendance
msgid "Attendances"
msgstr "การลงเวลางาน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__8
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__8
msgid "August"
msgstr "สิงหาคม"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "August 2023 Payslip"
msgstr "สลิปเงินเดือนเดือนสิงหาคม 2566"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr "ความพร้อมใช้งานในโครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__available_in_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Available in attachments"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_1920
msgid "Avatar"
msgstr "อวตาร"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_128
msgid "Avatar 128"
msgstr "อวตาร 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr "ค่าแรงขั้นพื้นฐานเฉลี่ย"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr "ค่าแรงสุทธิเฉลี่ย"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Bank account"
msgstr "บัญชีธนาคาร"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "พื้นฐาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_basic_salary_rule
msgid "Basic Salary"
msgstr "เงินเดือนขั้นพื้นฐาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
msgid "Basic Wage"
msgstr "ค่าแรงขั้นพื้นฐาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr "ค่าจ้างพื้นฐานสำหรับการการลา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr "ชุด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr "ชื่อชุด"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr "ชุด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "เบลเยี่ยม เงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "รายสองเดือน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "รายสัปดาห์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Board meeting summary:"
msgstr "สรุปการประชุมคณะกรรมการ:"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_payment_report_wizard__export_format__csv
msgid "CSV"
msgstr "CSV"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "การคำนวน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr "เปลี่ยนปฏิทินแล้ว"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Canceled"
msgstr "ถูกยกเลิก"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr "ยกเลิก"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot cancel a payslip that is done."
msgstr "ไม่สามารถยกเลิกสลิปเงินเดือนที่ทำเสร็จแล้ว"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot mark payslip as paid if not confirmed."
msgstr "ไม่สามารถทำเครื่องหมายสลิปเงินเดือนว่าชำระแล้วหากไม่ได้รับการยืนยัน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"โปรดระวัง มีการใช้ Code ในการอ้างอิงจำนวนมาก "
"การเปลี่ยนแปลงอาจนำไปสู่การเปลี่ยนแปลงที่ไม่พึงประสงค์ได้"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_headcount_line__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""
"จัดหมวดหมู่พนักงานของคุณตามประเภท ฟิลด์นี้ยังมีผลต่อสัญญาด้วย เฉพาะพนักงาน "
"นักศึกษา และผู้ฝึกงานเท่านั้นที่จะมีประวัติสัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "หมวดหมู่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_refund
msgid ""
"Check if the value of the salary attachment must be taken into account as "
"negative (-X)"
msgstr "ตรวจสอบว่าค่าแนบเงินเดือนต้องนำมาพิจารณาเป็นค่าลบ (-X) หรือไม่"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Check the <strong>Work Entries</strong> linked to your newly created "
"Contract."
msgstr ""
"ตรวจสอบ<strong>รายการงาน</strong>ที่เชื่อมโยงกับสัญญาที่สร้างขึ้นใหม่ของคุณ"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_child_support
msgid "Child Support"
msgstr "ค่าเลี้ยงดูบุตร"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "เด็ก ๆ "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "เลือกการแปลเงินเดือน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click here to create a new <strong>Contract</strong>."
msgstr "คลิกที่นี่เพื่อสร้าง<strong>สัญญา</strong>ใหม่"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click here to generate a <strong>Batch</strong> for the displayed Employees."
msgstr "คลิกที่นี่เพื่อสร้าง<strong>กลุ่ม</strong>สำหรับพนักงานที่แสดง"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on <strong>Salary Information</strong> to access additional fields."
msgstr "คลิกที่<strong>ข้อมูลเงินเดือน</strong>เพื่อเข้าถึงสาขาเพิ่มเติม"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on Employees to pick one of your <strong>Employees</strong>."
msgstr "คลิกที่ข้อมูลพนักงานเพื่อเลือก<strong>พนักงาน</strong>ของคุณ"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on Payroll to manage your employee's <strong>Work Entries</strong>, "
"<strong>Contracts</strong> and <strong>Payslips</strong>."
msgstr ""
"คลิกที่บัญชีเงินเดือนเพื่อจัดการ<strong>รายการงาน</strong> "
"<strong>สัญญา</strong> และ<strong>สลิปเงินเดือน</strong>ของพนักงานของคุณ"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Payslip</strong>."
msgstr "คลิกที่สลิปเงินเดือน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Work Entries</strong> menu."
msgstr "คลิกที่เมนู<strong>รายการงาน</strong>"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Close"
msgstr "ปิด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid_date
msgid "Close Date"
msgstr "วันปิด"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr "ใกล้เคียงที่สุด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__structure_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr "โค้ด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "โค้ด:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__color
msgid "Color"
msgstr "สี"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr "บริษัท"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "ผลงานของบริษัท"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
msgid "Company Full Time"
msgstr "บริษัทเต็มเวลา"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr "เสร็จสิ้น"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "การคำนวณ"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "คำนวนแผ่น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr "คำนวณบน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "เงื่อนไขอิงตาม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_other_input_id
msgid "Condition Other Input"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "เงื่อนไข"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Confirm the <strong>Payslip</strong>."
msgstr "ยืนยันสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr "ความขัดแย้ง"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_work_entries_in_conflict
msgid "Conflicts"
msgstr "ขัดแย้ง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__contract_ids
msgid "Contract"
msgstr "สัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_domain_ids
msgid "Contract Domain"
msgstr "โดเมนสัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr "ระยะเวลาแจ้งการหมดอายุของสัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_names
msgid "Contract Names"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr "ประเภทสัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Contract Wage ("
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "ประวัติสัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr "การจัดทำดัชนีสัญญา"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr "สัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "ลงทะเบียนสมทบ"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr "การลงทะเบียนสมทบ"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "ค่าเบี้ยเลี้ยง"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "ค่าขนส่งสำหรับน้ำเกรวี่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr "จำนวน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr "ประเทศ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr "รหัสประเทศ"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr "สร้างรายการร่าง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Create Individual Attachments"
msgstr "สร้างไฟล์แนบส่วนบุคคล"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Create Payment Report"
msgstr "สร้างรายงานการชำระเงิน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr "สร้างการชำระเงิน SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr "สร้างเอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__create_uid
msgid "Create Uid"
msgstr "สร้าง Uid"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr "สร้างสัญญาใหม่"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_salary_rule_form
msgid "Create a new salary rule"
msgstr "สร้างกฎเงินเดือนใหม่"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_rule_parameter_action
msgid "Create a new salary rule parameter"
msgstr "สร้างพารามิเตอร์กฎเงินเดือนใหม่"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Create a new salary structure"
msgstr "สร้างโครงสร้างเงินเดือนใหม่"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Create new todo note"
msgstr "สร้างบันทึกสิ่งที่ต้องทำใหม่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
msgid "Credit Note"
msgstr "ใบลดหนี้"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr "ใบลดหนี้"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "เครดิตเวลา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__time_credit
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "เครดิตเวลา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr "เดือนปัจจุบัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__is_name_custom
msgid "Custom Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__daily
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__daily
msgid "Daily"
msgstr "รายวัน"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_root
msgid "Dashboard"
msgstr "แดชบอร์ด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr "วันที่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr "จากวันที่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr "วันที่เริ่มต้น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr "ถึงวันที่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr "วันที่กำหนดงานนี้เสร็จสมบูรณ์หรือยกเลิก"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr "วัน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Day where the 'Year To Date' will be reset every year."
msgstr "วันที่ 'ปีถึงวันที่' จะถูกรีเซ็ตทุกปี"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,help:hr_payroll.field_res_config_settings__ytd_reset_day
msgid ""
"Day where the YTD will be reset every year. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"วันที่ YTD จะถูกรีเซ็ตทุกปี หากเป็นศูนย์หรือติดลบ ระบบจะเลือกวันที่ 1 ของเดือนแทน\n"
"        หากมากกว่าวันสุดท้ายของเดือน ระบบจะเลือกวันสุดท้ายของเดือนแทน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Days"
msgstr "วัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr "วันที่ลาได้รับค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr "วันขาดงานที่คาดไม่ถึง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr "วันที่ลาไม่ได้รับค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__12
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__12
msgid "December"
msgstr "ธันวาคม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_id
msgid "Declaration Model Id"
msgstr "รหัสโมเดลการประกาศ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_model
msgid "Declaration Model Name"
msgstr "ชื่อรุ่นการประกาศ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__line_ids
msgid "Declarations"
msgstr "ประกาศ"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_deduction_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.DED
msgid "Deduction"
msgstr "ค่าลดหย่อน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr "การจ่ายตามกำหนดเวลาเริ่มต้น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
msgid "Default Wage Type"
msgstr "ประเภทค่าจ้างเริ่มต้น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "ประเภทการเข้างานเริ่มต้น"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Define a <strong>Wage</strong>."
msgstr "กำหนดค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "กำหนดความถี่ในการจ่ายค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""
"กำหนดกฎเกณฑ์ที่จะใช้กับสลิปเงินเดือนนี้ตามสัญญาที่เลือก ถ้าสัญญาว่างเปล่า "
"ฟิลด์นี้ไม่จำเป็นอีกต่อไป "
"และกฎที่ถูกต้องทั้งหมดของโครงสร้างของสัญญาของพนักงานจะถูกนำมาใช้"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr "แผนก"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Department:"
msgstr "แผนก:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "คำอธิบาย"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__disabled
msgid "Disabled"
msgstr "ปิดการใช้งาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr "ละทิ้ง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Display Payslip PDF File on a payslip form"
msgstr "แสดงไฟล์ PDF สลิปเงินเดือนในแบบฟอร์มสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr "แสดงสลิปเงินเดือน"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_payslip_display
msgid "Display payslip PDF"
msgstr "แสดงสลิปเงินเดือน PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr "เอกสาร"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "สลิปเงินเดือนชุดที่เสร็จสิ้นแล้ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "เสร็จสิ้นสลิป"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Double Holiday Pay"
msgstr "จ่ายวันหยุดสองเท่า"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr "ลด"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr "ร่าง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "ร่างสลิปเงินเดือนชุด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "ร่างสลิป"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Earnings"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
msgid "Edit Payslip Lines"
msgstr "แก้ไขรายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr "ตัวช่วยแก้ไขรายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr "ตัวช่วยแก้ไขวันทำงานของตัวช่วยบรรทัดสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr "ตัวช่วยแก้ไขรายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr "รายการตัวช่วยแก้ไขรายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr "แก้ไขแล้ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr "ตัวแก้ไขของรายการสลิปเงินเดือนในสลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "Eligible Employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_count
msgid "Eligible Employees Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr "cc อีเมล"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Employee Code"
msgstr "รหัสพนักงาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "สัญญาของพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__employee_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_count
msgid "Employee Count"
msgstr "จำนวนพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Employee Declarations"
msgstr "คำประกาศของพนักงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "ฟังก์ชันพนักงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Employee Information"
msgstr "ข้อมูลพนักงาน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr "สลิปเงินเดือนบุคลากร"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employee Trends"
msgstr "แนวโน้มของพนักงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Employee Type"
msgstr "ประเภทพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee address"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee name"
msgstr ""

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_with_different_company_on_contract
msgid "Employee whose contracts and company are differents"
msgstr "พนักงานที่มีสัญญาและบริษัทต่างกัน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr "ค่าแรงขั้นต่ำรายชั่วโมงของพนักงานรวม"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible schedule, allowing them to work without any time limit, anytime of the week.\n"
"        "
msgstr ""
"ตารางการทำงานของพนักงาน\n"
"        หากปล่อยให้ว่าง พนักงานจะถือว่ามีตารางงานที่ยืดหยุ่นอย่างเต็มที่ ช่วยให้ทำงานได้โดยไม่มีการจำกัดเวลา ตลอดทั้งวันทั้งสัปดาห์\n"
"        "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "พนักงาน"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_missing_from_open_batch
msgid "Employees (With Running Contracts) missing from open batches"
msgstr "พนักงาน (ที่มีสัญญาที่ดำเนินอยู่) หายไปจากชุดงานที่เปิดอยู่"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Employees Selection"
msgstr ""

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_ambiguous_contract
msgid "Employees With Both New And Running Contracts"
msgstr "พนักงานที่มีทั้งสัญญาใหม่และสัญญาที่กำลังใช้งานอยู่"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employees_multiple_payslips
msgid "Employees With Multiple Open Payslips of Same Type"
msgstr "พนักงานที่มีสลิปเงินเดือนประเภทเดียวกันหลายใบ"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_bank_account
msgid "Employees Without Bank account Number"
msgstr "พนักงานที่ไม่มีหมายเลขบัญชีธนาคาร"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_identification
msgid "Employees Without Identification Number"
msgstr "พนักงานที่ไม่มีหมายเลขประจำตัว"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_contract
msgid "Employees Without Running Contracts"
msgstr "พนักงานที่ไม่มีสัญญาจ้าง"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_nearly_expired_contracts
msgid "Employees with running contracts coming to an end"
msgstr "พนักงานที่สัญญาทำงานกำลังจะสิ้นสุดลง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employer Cost"
msgstr "ต้นทุนนายจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid ""
"Enable this option if you don't want to display the Basic Salary on the "
"printed pdf."
msgstr ""
"เปิดใช้งานตัวเลือกนี้หากคุณไม่ต้องการแสดงเงินเดือนพื้นฐานในรูปแบบ pdf "
"ที่พิมพ์ออกมา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr "วันที่สิ้นสุด"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr "วันที่สิ้นสุดต้องไม่อยู่ก่อนวันที่เริ่มต้น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr "ผิดพลาด"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr "ข้อผิดพลาด! คุณไม่สามารถสร้างลำดับชั้นแบบเรียกซ้ำของประเภทกฎเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr "วันที่สิ้นสุดโดยประมาณ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr "ในที่สุดบุคคลที่สามที่เกี่ยวข้องกับการจ่ายเงินเดือนของพนักงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__export_id
msgid "Export"
msgstr "ส่งออก"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__payment_report
msgid "Export .csv file related to this batch"
msgstr "ส่งออกไฟล์ .csv ที่เกี่ยวข้องกับชุดนี้"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__payment_report
msgid "Export .csv file related to this payslip"
msgstr "ส่งออกไฟล์ .csv ที่เกี่ยวข้องกับสลิปเงินเดือนนี้"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_list_view
msgid "Export Employee Work Entries"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_file
msgid "Export File"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_filename
msgid "Export Filename"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "รูปแบบการส่งออก"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr "ส่งออกสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__2
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__2
msgid "February"
msgstr "กุมภาพันธ์"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "First, we'll create a new <strong>Contract</strong>."
msgstr "ก่อนอื่น เราจะสร้าง<strong>สัญญา</strong>ใหม่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "จำนวนคงที่"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Fixed Wage"
msgstr "ค่าจ้างคงที่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "ตัวอย่างเช่น ป้อน 50.0 เพื่อใช้เปอร์เซ็นต์ 50%"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "บัญชีเงินเดือนสำหรับประเทศฝรั่งเศส"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr "จาก"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "From %(from_date)s to %(end_date)s"
msgstr "จาก %(from_date)s ถึง %(end_date)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "ทั่วไป"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "สร้าง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Generate Export File"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Generate PDFs"
msgstr "สร้าง PDF"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/hr_work_entries_gantt.xml:0
#: code:addons/hr_payroll/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model:ir.actions.server,name:hr_payroll.action_hr_payslip_run_generate
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr "สร้างสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr "สร้างสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "สร้างสลิปเงินเดือนสำหรับบุคลากรทุกคน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_generated
msgid "Generated PDF"
msgstr "PDF ที่สร้างขึ้น"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr "สร้างสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Give insurance card to new registered employees"
msgstr "มอบบัตรประกันให้กับพนักงานขึ้นทะเบียนใหม่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__gross_wage
msgid "Gross Wage"
msgstr "ค่าแรงรวม"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__group_payslip_display
msgid "Group Payslip Display"
msgstr "แสดงสลิปเงินเดือนแบบกลุ่ม"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "ตัวช่วยสร้างรายงานการจ่ายเงินเดือน HR"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR การเข้างาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR ประเภทการเข้างาน"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr "ครึ่งวัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_done_payslip
msgid "Has Done Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr "มียอดสุทธิติดลบในการรายงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr "มีสลิปเงินคืน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr "มีเอกสารแนบที่คล้ายกัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr "มีคำเตือนเอกสารแนบที่คล้ายกัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr "มียอดรวม"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payroll_headcount_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_headcount_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_tree
msgid "Headcount"
msgstr "จำนวนพนักงาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_line
msgid "Headcount Line"
msgstr "บรรทัดจำนวนพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s from %(date_from)s to %(date_to)s"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s on the %(date)s"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_view_tree
msgid "Headcount's Employees"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount's employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid "Hide Basic On Pdf"
msgstr "ซ่อนพื้นฐานในรูปแบบ Pdf"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__hourly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr "ค่าแรงรายชั่วโมง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours"
msgstr "ชั่วโมง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours / Week"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr "ชั่วโมงต่อสัปดาห์"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "ค่าเช่าบ้าน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr "ไอดี"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"If empty, the default salary structure from the Salary structure type of the"
" employee will be used"
msgstr ""
"หากว่างเปล่า "
"จะใช้โครงสร้างเงินเดือนเริ่มต้นจากประเภทโครงสร้างเงินเดือนของพนักงาน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,help:hr_payroll.field_res_users__is_non_resident
msgid "If recipient lives in a foreign country"
msgstr "หากผู้รับอาศัยอยู่ในต่างประเทศ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_quantity
msgid ""
"If set, hide currency and consider the manual input as a quantity for every "
"rule computation using this input."
msgstr ""
"หากตั้งค่า "
"ให้ซ่อนสกุลเงินและพิจารณาการป้อนข้อมูลด้วยตนเองเป็นปริมาณสำหรับการคำนวณกฎทุกข้อที่ใช้การป้อนข้อมูลนี้"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะอนุญาตให้คุณซ่อนกฎเงินเดือนโดยไม่ต้องลบออก"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__disabled
msgid "If the employee is declared disabled by law"
msgstr "หากพนักงานถูกประกาศให้ทุพพลภาพตามกฎหมาย"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_tree
msgid "Index Contracts"
msgstr "ดัชนีสัญญา"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr "ดัชนีสัญญา"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr "ดัชนีสัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "บัญชีเงินเดือนสำหรับประเทศอินเดีย"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "ตั้งว่าสลิปเงินเดือนนี้มีการคืนเงิน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "ป้อนข้อมูล"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "บันทึกย่อภายใน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "ใบแจ้งหนี้การสมัครสมาชิกทางอินเตอร์เน็ต"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr "เป็นเต็มเวลา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr "จ่ายแล้ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr "คือการลงทะเบียน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr "เป็นประจำ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr "เป็นผู้ใช้ขั้นสูง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_wrong_duration
msgid "Is Wrong Duration"
msgstr "ระยะเวลาไม่ถูกต้อง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_quantity
msgid "Is quantity?"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days['WORK100'].number_of_days."
msgstr ""
"ใช้ในการคำนวณหาเปอร์เซ็นต์และจำนวนคงที่ เช่น "
"กฎสำหรับบัตรรับประทานอาหารที่มีจำนวนเงินคงที่ 1€ "
"ต่อวันทำงานสามารถกำหนดปริมาณได้ในตัวสั่งงาน เช่น "
"work_days['WORK100'].number_of_days"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs['SALEURO'].amount * contract.wage * 0.01."
msgstr ""
"มันถูกใช้ในการคำนวณ เช่น กฎสำหรับพนักงานขายที่มีค่าคอมมิชชัน 1%% "
"ของเงินเดือนพื้นฐานต่อผลิตภัณฑ์สามารถกำหนดไว้ในตัวสั่งงาน เช่น result = "
"inputs['SALEURO'].amount * Contract.wage * 0.01"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__1
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__1
msgid "January"
msgstr "มกราคม"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Job"
msgstr "งาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr "ตำแหน่งงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__job_id
msgid "Job Title"
msgstr "ชื่อตำแหน่งงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Job Title:"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__7
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__7
msgid "July"
msgstr "กรกฎาคม"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__6
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__6
msgid "June"
msgstr "มิถุนายน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr "สลิปเงินเดือน 365 วันที่ผ่านมา"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Last Departures"
msgstr "การออกเดินทางครั้งล่าสุด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr "เดือนที่แล้ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr "ไตรมาสที่แล้ว"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__line_ids
msgid "Line"
msgstr "รายการ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr "ชื่อรายการ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__lines_count
msgid "Lines Count"
msgstr "จำนวนบรรทัด"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"การเชื่อมโยงหมวดหมู่เงินเดือนกับตัวหลักจะใช้เพื่อวัตถุประสงค์ในการรายงานเท่านั้น"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Links:"
msgstr "ลิงค์:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order? "
msgstr "สั่งซื้อการชำระเงินแล้วหรือยัง?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__3
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__3
msgid "March"
msgstr "มีนาคม"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr "ทำเครื่องหมายว่าเสร็จแล้ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr "ทำเครื่องหมายว่าชำระเงินแล้ว"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__master_department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Master Department"
msgstr "สาขาวิชาปริญญาโท"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "ช่วงสูงสุด"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__5
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__5
msgid "May"
msgstr "พฤษภาคม"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "บัตรกำนัลอาหาร"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "ช่วงต่ำสุด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "ใบแจ้งหนี้การสมัครสมาชิกมือถือ"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr "โมเดลสำหรับพิมพ์ hr.payslip.line จัดกลุ่มตามการลงทะเบียน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr "รายเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr "จำนวนรายเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr "ชื่อ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""
"ชื่อที่จะตั้งในสลิปเงินเดือน ตัวอย่าง: 'โบนัสสิ้นปี' หากไม่ได้ตั้งค่าไว้ "
"ค่าเริ่มต้นคือ 'สลิปเงินเดือน'"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Name:"
msgstr "ชื่อ:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr "ลบสุทธิที่จะรายงานจำนวน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr "ลบสุทธิที่จะรายงานการแสดงผล"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr "ลบสุทธิในการรายงานข้อความ"

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr "ติดลบสุทธิที่จะรายงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_refund
msgid "Negative Value"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "สุทธิ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr "สุทธิ-"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_net_salary
msgid "Net Salary"
msgstr "เงินเดือนสุทธิ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr "ค่าจ้างสุทธิ"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
msgid "New"
msgstr "ใหม่"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_new_contracts
msgid "New Contracts"
msgstr "สัญญาใหม่"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "New Employees"
msgstr "พนักงานใหม่"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "New Payslip"
msgstr "สลิปเงินเดือนใหม่"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__no_end_date
msgid "No End Date"
msgstr "ไม่มีวันสิ้นสุด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "No ID number on the employee !!!"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr "ไม่มีการปัดเศษ"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr "ไม่อนุญาตให้ทำซ้ำหมายเลขทะเบียน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__default_no_end_date
msgid "No end date by default"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid "No rule parameter with code \"%(code)s\" was found for %(date)s"
msgstr "ไม่พบพารามิเตอร์กฎที่มีรหัส \"%(code)s\" สำหรับวันที่ %(date)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,field_description:hr_payroll.field_res_users__is_non_resident
msgid "Non-resident"
msgstr "ไม่ใช่ผู้มีถิ่นที่อยู่"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__note
msgid "Note"
msgstr "โน้ต"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"Note a description of your parameter, when it's used, how is it computed, "
"what's the source, ..."
msgstr ""
"จดคำอธิบายพารามิเตอร์ของคุณ ใช้งานเมื่อใด วิธีคำนวณ แหล่งที่มาคืออะไร ..."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr "หมายเหตุ: มีสลิปเงินเดือนก่อนหน้าที่มียอดติดลบรวม%s ที่จะรายงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "โน้ต"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr "ไม่มีอะไรจะแสดง"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__11
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__11
msgid "November"
msgstr "พฤศจิกายน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "จำนวนวัน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "จำนวนชั่วโมง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""
"จำนวนวันก่อนวันที่สิ้นสุดสัญญาที่มีการทริกเกอร์คำเตือนการหมดอายุของสัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr "จำนวนวันก่อนวันหมดอายุใบอนุญาตทำงานที่มีการเตือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr "จำนวนชั่วโมงทำงานในบริษัทที่กำหนดให้ถือเป็นงานเต็มเวลา"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Number of times the salary attachment will appear on the payslip."
msgstr "จำนวนครั้งที่เอกสารเงินเดือนจะแสดงบนสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Occurrences"
msgstr "การเกิดขึ้น"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__10
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__10
msgid "October"
msgstr "ตุลาคม"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer: Manage all contracts"
msgstr "เจ้าหน้าที่ : จัดการสัญญาทั้งหมด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "On the"
msgstr "บน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the first tab is the amount of worked time giving you a <strong>gross "
"amount</strong>."
msgstr "บนแท็บแรกคือระยะเวลาทำงานซึ่งให้<strong>จำนวนเงินรวม</strong>กับคุณ"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the second tab is the computation of the rules linked to the Structure "
"resulting in a <strong>net amount</strong>."
msgstr ""
"บนแท็บที่สองคือการคำนวณกฎที่เชื่อมโยงกับโครงสร้างซึ่งส่งผลให้เป็น<strong>จำนวนเงินสุทธิ</strong>"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the smartbutton, you can find all the <strong>Payslips</strong> included "
"in the Batch."
msgstr ""
"บนปุ่มอัจฉริยะ คุณจะพบ<strong>สลิปเงินเดือน</strong>ทั้งหมดที่รวมอยู่ในชุด"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_actions_server_action_open_reporting
msgid "Open Payroll Reporting"
msgstr "เปิดรายงานเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr "ข้อมูลอื่น ๆ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Other Information"
msgstr "ข้อมูลอื่นๆ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__input
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__input
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr "ข้อมูลเข้าอื่น ๆ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr "รายการขาเข้าอื่น"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr "ประเภทข้อมูลขาเข้าอื่น ๆ "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "ป้อนข้อมูลอื่น"

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr "หมดสัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__pdf_error
msgid "PDF Error Message"
msgstr "ข้อความแสดงข้อผิดพลาด PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_file
msgid "PDF File"
msgstr "ไฟล์ PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDF generation started. It will be available shortly."
msgstr "เริ่มต้นการสร้าง PDF และจะสามารถใช้ได้ในไม่ช้า"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr "ชำระแล้ว"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr "จำนวนเงินที่ชำระ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
msgid "Paid Time Off"
msgstr "การลาที่ได้ค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr "ค่าพารามิเตอร์"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "หลัก"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Part Time"
msgstr "พาร์ทไทม์"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit_type_id
msgid "Part Time Work Entry Type"
msgstr "ประเภทงานพาร์ทไทม์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "Part time"
msgstr "พาร์ทไทม์"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Pay Period:"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr "PaySlip Lines โดยการลงทะเบียนสมทบ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr "ชื่อสลิปจ่ายเงิน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
msgid "Payment Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_date
msgid "Payment Report Date"
msgstr "จำนวนเงินที่ต้องชำระ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_filename
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_filename
msgid "Payment Report Filename"
msgstr "ชื่อไฟล์รายงานการชำระเงิน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "เงินเดือน"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr "เงินเดือน - เทคนิค: รีเซ็ตรายการงาน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr "การวิเคราะห์บัญชีเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "รายงานการวิเคราะห์บัญชีเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
msgid "Payroll Code"
msgstr "รหัสบัญชีเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.client,name:hr_payroll.hr_payroll_dashboard_open
msgid "Payroll Dashboard"
msgstr "แดชบอร์ดเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_dashboard_warning
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_tree
msgid "Payroll Dashboard Warning"
msgstr "คำเตือนแดชบอร์ดบัญชีเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_dashboard_warning
msgid "Payroll Dashboard Warnings"
msgstr "คำเตือนแดชบอร์ดบัญชีเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr "การประกาศเงินเดือน Mixin"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr "ประกาศเงินเดือนพนักงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "รายการเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount
msgid "Payroll Headcount"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_note
msgid "Payroll Note"
msgstr "บันทึกเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr "กฎการจ่ายเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr "SEPA เงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "โครงสร้างเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "กฎการจ่ายเงินเดือนที่ใช้กับประเทศของคุณ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Payroll tips &amp; tricks:"
msgstr "เคล็ดลับ &amp; เทคนิคของเงินเดือน:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr "บัญชีเงินเดือนกับบัญชี"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_iso20022
msgid "Payroll with SEPA payment"
msgstr "เงินเดือนด้วยการชำระเงินผ่าน SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
msgid "Payroll: Generate pdfs"
msgstr "เงินเดือน: สร้าง pdfs"

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr "บัญชีเงินเดือน: สลิปเงินเดือนใหม่"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
msgid "Payroll: Update data"
msgstr "เงินเดือน: อัปเดตข้อมูล"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_new
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr "สลิปเงินเดือน 'วันที่จาก' จะต้องอยู่ก่อนหน้า 'วันที่ถึง'"

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_report_light_payslip
msgid "Payslip (Light)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Payslip Amount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr "ชุดสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "ชุดสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr "จำนวนสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslip Generation"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr "ป้อนข้อมูลสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr "ชื่อข้อมูลขาเข้าสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Payslip Input Type"
msgstr "ประเภทข้อมูลขาเข้าสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "ป้อนข้อมูลสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
msgid "Payslip Language"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "รายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "รายการสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "ชื่อสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr "ประเภทข้อมูลขาเข้าอื่น ๆ ของสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payslip PDF Display"
msgstr "การแสดงสลิปเงินเดือน PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Payslip Period"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_run_id
msgid "Payslip Run"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "วันทำงานในสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Payslip amount must be strictly positive."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "ชุดสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Count"
msgstr "จำนวนสลิปเงินเดือน"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_previous_contract
msgid "Payslips Generated On Previous Contract"
msgstr "สลิปเงินเดือนที่สร้างขึ้นในสัญญาก่อนหน้า"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr "สลิปเงินเดือนที่ต้องจ่าย"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_negative_net
msgid "Payslips With Negative NET"
msgstr "สลิปเงินเดือนที่มี NET ติดลบ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "สลิปเงินเดือนตามบุคลากร"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_filename
msgid "Pdf Filename"
msgstr "ชื่อไฟล์ pdf"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_to_generate
msgid "Pdf To Generate"
msgstr "PDF เพื่อสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr "เปอร์เซ็นต์"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "เป็นเปอร์เซ็นต์ (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "คิดเป็นเปอร์เซ็นต์จาก"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "ช่วงเวลา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_start
msgid "Period Start"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_stop
msgid "Period Stop"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__planning
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__planning
msgid "Planning"
msgstr "การวางแผน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Please select the declarations for which you want to generate a PDF."
msgstr "โปรดเลือกการประกาศที่คุณต้องการสร้าง PDF"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Populate"
msgstr "ถิ่นฐาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "ลงสลิปเงินเดือนทางบัญชี"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Previous Negative Payslip to Report"
msgstr "สลิปเงินเดือนที่ติดลบก่อนหน้าที่จะรายงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Print"
msgstr "พิมพ์"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "ภาษีมืออาชีพ"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Prorated end-of-year bonus"
msgstr "โบนัสสิ้นปีตามสัดส่วน"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "กองทุนสำรองเลี้ยงชีพ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__evaluation_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Python Code"
msgstr "โค้ด Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "เงื่อนไขของ Python"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Python Expression"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr "โครงสร้างข้อมูล Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Quantity"
msgstr "ปริมาณ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr "ปริมาณ/อัตรา"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Quarter %(quarter)s of %(year)s"
msgstr "ไตรมาส %(quarter)s ของปี %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr "รายไตรมาส"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr "คิวสำหรับ Pdf"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_generate
msgid "Queued PDF generation"
msgstr "คิวการสร้าง PDF"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "ช่วง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "ช่วงอิงตาม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Rate"
msgstr "อัตรา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "อัตรา (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr "คำนวณใหม่ทั้งแผ่น"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""
"คำนวณรายการสลิปเงินเดือนใหม่เท่านั้น ไม่ใช่วันทำงาน / รายการข้อมูลขาเข้า"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Recorded a new payment of %s."
msgstr "บันทึกการชำระเงินใหม่ของ%s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "การอ้างอิง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_month
msgid "Reference Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_year
msgid "Reference Year"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "คืนเงิน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Refund: %(payslip)s"
msgstr "การคืนเงิน: %(payslip)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Refunds"
msgstr "การคืนเงิน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr "หมายเลขทะเบียนลูกจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr "โครงสร้างการจ่ายปกติ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr "วันทำงานปกติ"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_reimbursement_salary_rule
msgid "Reimbursement"
msgstr "ค่าชดเชย"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Related Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr "จำนวนคงเหลือ"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr "จำนวนที่คงเหลือต้องเป็นค่าบวก"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr "จำนวนเงินคงเหลือที่ต้องจ่าย"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Remove \"Conflicting\" filter"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Report Date"
msgstr "วันที่รายงาน"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr "การรายงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Resolve Conflicts"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr "เวลาทำงานของทรัพยากร"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr "ประเภทการปัด"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr "การปัดเศษ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "กฎ"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr "หมวดหมู่กฎ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr "ชื่อกฎ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr "พารามิเตอร์กฎ"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr "พารามิเตอร์กฎ"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr "กฏ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr "การดำเนินการ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr "สำเนา SIM Card"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr "เงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_attachment_new
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Salary Attachment"
msgstr "เอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__salary_attachments_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr "จำนวนเอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr "จำนวนเอกสารแนบเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Attachments"
msgstr "เอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "หมวดเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "การคำนวนเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "กฎเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "หมวดเกณฑ์เงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "หมวดกฎเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr "พารามิเตอร์กฎเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
msgid "Salary Rule Parameter Value"
msgstr "ค่าพารามิเตอร์กฎเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr "พารามิเตอร์กฎเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "เกณฑ์เงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Salary Slip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr "โครงสร้างเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__structure_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_tree_inherit
msgid "Salary Structure Type"
msgstr "ประเภทโครงสร้างเงินเดือน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr "โครงสร้างเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
msgid "Schedule Pay"
msgstr "กำหนดการชำระเงิน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "Search Declarations"
msgstr "ประกาศการค้นหา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_search
msgid "Search Headcount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
msgid "Search Payroll Dashboard Warning"
msgstr "ค้นหาคำเตือนแดชบอร์ดบัญชีเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "ค้นหาชุดสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "ค้นหารายการสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "ค้าหาสลิปเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr "ค้นหาเอกสารแนบเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "ค้นหาเกณฑ์เงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr "ประเภทการค้นหาโครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "ประจำสองปี"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-monthly
msgid "Semi-monthly"
msgstr "รายครึ่งเดือน"

#. module: hr_payroll
#: model:mail.template,description:hr_payroll.mail_template_new_payslip
msgid "Sent to employee to notify them about their new payslip"
msgstr "ส่งให้พนักงานแจ้งเกี่ยวกับสลิปเงินเดือนใหม่"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__9
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__9
msgid "September"
msgstr "กันยายน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific department if you wish to select all the employees from this "
"department (and subdepartments) at once."
msgstr ""
"ตั้งค่าแผนกเฉพาะหากคุณต้องการเลือกพนักงานทั้งหมดจากแผนกนี้ (และแผนกย่อย) "
"ในครั้งเดียว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific job if you wish to select all the employees from this job at "
"once."
msgstr ""
"กำหนดงานที่เฉพาะเจาะจงหากคุณต้องการเลือกพนักงานทั้งหมดจากงานนี้ในครั้งเดียว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific structure type if you wish to select all the employees from "
"this structure type at once."
msgstr ""
"ตั้งค่าประเภทโครงสร้างที่เจาะจงหากคุณต้องการเลือกพนักงานทั้งหมดจากประเภทโครงสร้างนี้ในครั้งเดียว"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Set the Contract as <strong><q>Running</q></strong>."
msgstr "ตั้งค่าสัญญาว่า<strong><q>กำลังใช้งานอยู่</q></strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "กำหนดให้เป็นฉบับร่าง"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "การตั้งค่า"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr "สลิปเพื่อยืนยัน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a bank account."
msgstr "พนักงานบางคน (%s) ไม่มีบัญชีธนาคาร"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid ""
"Some work entries are in conflict. Please resolve the conflicts before "
"exporting."
msgstr "งานบางรายการมีความขัดแย้ง โปรดแก้ไขความขัดแย้งก่อนส่งออก"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Some work entries could not be validated."
msgstr "ไม่สามารถตรวจสอบรายการงานบางรายการได้"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__standard_calendar_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__standard_calendar_id
msgid "Standard Calendar"
msgstr "ปฏิทินมาตรฐาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "State"
msgstr "รัฐ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr "สถานะ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Structure"
msgstr "โครงสร้าง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr "ชื่อโครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr "ประเภทโครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr "จำนวนประเภทโครงสร้าง"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr "ประเภทโครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr "โครงสร้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr "รวมชั่วโมงทำงาน"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr "ผลรวมของหมวดค่าเผื่อ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr "ผลรวมวันที่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Tag color. No color means black."
msgstr "สีของแท็ก ไม่มีสีจะถือว่าเป็นสีดำ"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_gross_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
msgid "Taxable Salary"
msgstr "เงินเดือนที่ต้องเสียภาษี"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Template"
msgstr "เทมเพลต"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"โค้ดประเทศ ISO ในสองตัวอักษร\n"
"คุณสามารถใช้ช่องนี้เพื่อการค้นหาอย่างรวดเร็ว"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"The Standard Calendar is the calendar used by the people working at a 100% "
"rate. It's used here to compute your part-time percentage."
msgstr ""
"ปฏิทินมาตรฐานคือปฏิทินที่ใช้โดยพนักงานในอัตรา 100% "
"ใช้สำหรับคำนวณเปอร์เซ็นต์นอกเวลาของคุณที่นี่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""
"รายการงานที่ถูกตรวจสอบเป็นการขาดงานโดยไม่คาดคิดจะถูกนับในรายงานการขาดงานในที่ทำงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
msgid ""
"The YTD reset day must be a valid day of the month : since the current month"
" is %(month)s, it should be between 1 and %(day)s."
msgstr ""
"วันรีเซ็ต YTD จะต้องเป็นวันที่ถูกต้องของเดือน เนื่องจากเดือนปัจจุบันคือ "
"%(month)s จึงควรอยู่ระหว่าง 1 ถึง %(day)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"รหัสกฎเงินเดือนสามารถใช้เป็นข้อมูลอ้างอิงในการคำนวณกฎเกณฑ์อื่นได้ ในกรณีนั้น"
" จะพิจารณาตัวพิมพ์เล็กและตัวพิมพ์ใหญ่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr "โค้ดที่ใช้ในกฎเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "วิธีการคำนวณสำหรับจำนวนกฎ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr "สัญญาวันทำงานนี้ควรใช้กับ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr "สัญญาวันทำงานนี้ควรใช้กับ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__paid_date
msgid "The date on which the payment is made to the employee."
msgstr "วันที่ชำระเงินให้กับพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The duration of the payslip is not accurate according to the structure type."
msgstr "ระยะเวลาของสลิปเงินเดือนไม่ถูกต้องตามประเภทโครงสร้าง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""
"พนักงานต่อไปนี้มีสัญญานอกระยะเวลาสลิปเงินเดือน:\n"
"%s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""
"ค่าต่อไปนี้ไม่ถูกต้อง:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "จำนวนสูงสุดที่ใช้กับเกณฑ์นี้"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "จำนวนต่ำสุดที่ใช้กับเกณฑ์นี้"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""
"จำนวนสุทธิจะได้รับคืนจากค่าตอบแทนที่เป็นบวกครั้งแรกที่สร้างขึ้นหลังจากนี้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The payslips should be in Draft or Waiting state."
msgstr "สลิปเงินเดือนควรอยู่ในสถานะร่างหรือรอ"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
msgid "The payslips(s) are now added to the batch"
msgstr "ขณะนี้มีการเพิ่มสลิปเงินเดือนลงในชุดแล้ว"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The period selected does not match the contract validity period."
msgstr "ระยะเวลาที่เลือกไม่ตรงกับระยะเวลาที่มีผลบังคับใช้ของสัญญา"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The selected payslips should be linked to the same batch"
msgstr "สลิปเงินเดือนที่เลือกควรเชื่อมโยงกับชุดเดียวกัน"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_headcount_date_range
msgid "The start date must be anterior to the end date."
msgstr "วันที่เริ่มต้นต้องอยู่ก่อนวันที่สิ้นสุด"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__time_credit_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"ประเภทรายการงานที่ใช้เมื่อสร้างรายการงานให้พอดีกับตารางการทำงานแบบเต็มเวลา"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr "รายการงานจะไม่ให้เงินใด ๆ แก่พนักงานในสลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "There is no declaration to generate for the given period"
msgstr "ไม่มีการประกาศที่จะสร้างในช่วงเวลาที่กำหนด"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"There is no valid payslip (done and net wage > 0) to generate the file."
msgstr ""
"ไม่มีสลิปเงินเดือนที่ถูกต้อง (เสร็จสิ้นและค่าจ้างสุทธิ > 0) เพื่อสร้างไฟล์"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "There should be at least one payslip to generate the file."
msgstr "ควรมีสลิปเงินเดือนอย่างน้อยหนึ่งใบเพื่อสร้างไฟล์"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"There's no contract set on payslip %(payslip)s for %(employee)s. Check that "
"there is at least a contract set on the employee form."
msgstr ""
"ไม่มีการกำหนดสัญญาในสลิปเงินเดือน %(payslip)s สำหรับ %(employee)s "
"ตรวจสอบว่ามีสัญญาที่กำหนดไว้ในแบบฟอร์มพนักงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is forbidden on validated payslips."
msgstr ""
"การดำเนินการนี้เป็นสิ่งต้องห้ามในสลิปเงินเดือนที่ผ่านการตรวจสอบความถูกต้องแล้ว"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is restricted to payroll managers only."
msgstr "การดำเนินการนี้จำกัดให้เฉพาะผู้จัดการบัญชีเงินเดือนเท่านั้น"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr "รหัสนี้ใช้ในกฎเงินเดือนเพื่ออ้างถึงพารามิเตอร์นี้"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""
"ข้อมูลนี้จะใช้ได้เฉพาะในโครงสร้างเหล่านั้น "
"หากว่างจะมีอยู่ในสลิปเงินเดือนทั้งหมด"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__time_credit
msgid "This is a credit time contract."
msgstr "นี่คือสัญญาสินเชื่อเวลา"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr "นี่คือรายการงานเวลาเครดิต"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This payslip can be erroneous :"
msgstr "สลิปเงินเดือนนี้อาจผิดพลาดได้:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
msgid "This payslip has been manually edited by %s."
msgstr "สลิปเงินเดือนนี้ได้รับการแก้ไขด้วยตนเองโดย %s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "This payslip is not validated. This is not a legal document."
msgstr "สลิปเงินเดือนนี้ไม่ผ่านการตรวจสอบ นี่ไม่ใช่เอกสารทางกฎหมาย"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr "รายงานนี้ทำการวิเคราะห์สลิปเงินเดือนของคุณ"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr "รายงานนี้ทำการวิเคราะห์รายการงานของคุณ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"ซึ่งจะใช้ในการคำนวณค่าฟิลด์ % โดยทั่วไปแล้วมันเป็นแบบพื้นฐาน "
"แต่คุณยังสามารถใช้ฟิลด์รหัสหมวดหมู่เป็นตัวพิมพ์เล็กเป็นชื่อตัวแปร (hra, ma, "
"lta ฯลฯ) และตัวแปรพื้นฐานได้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Time intervals to look for:%s"
msgstr "ช่วงเวลาที่จะมองหา:%s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr "ถึง"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr "เพื่อคำนวณ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr "ยืนยัน"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr "ที่จะจ่าย"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr "ที่จะจ่ายใน"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr "หากต้องการดูบางอย่างในรายงานนี้ ให้คำนวณสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Total"
msgstr "รวม"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr "ยอดรวม"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "วันทำงานทั้งหมด"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr "ชั่วโมงการทำงานทั้งหมด"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"payslip amount."
msgstr ""
"ยอดเงินรวมต้องเป็นบวกอย่างเคร่งครัดและมากกว่าหรือเท่ากับยอดเงินในสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr "จำนวนเงินที่ต้องจ่ายทั้งหมด"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr "รวมจำนวนชั่วโมงการเข้างานและวันหยุด (จ่ายหรือไม่จ่าย)"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr "พารามิเตอร์กฎสองข้อไม่สามารถมีโค้ดเดียวกันได้"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr "กฎสองข้อที่มีโค้ดเดียวกันไม่สามารถเริ่มในวันเดียวกันได้"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__other_input_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr "ประเภท"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr "การขาดงานที่คาดไม่ถึง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Unknown State"
msgstr "รัฐที่ไม่รู้จัก"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Unpaid"
msgstr "ยังไม่ได้ชำระ"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
msgid "Unpaid Time Off"
msgstr "การลาที่ไม่ได้ค่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr "ไม่จ่ายในประเภทการเข้างาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr "ไม่จ่ายในประเภทการเข้างาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr "ไม่จ่ายในประเภทโครงสร้าง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Untrusted bank account for the following employees:\n"
"%s"
msgstr ""
"บัญชีธนาคารที่ไม่น่าเชื่อถือสำหรับพนักงานดังต่อไปนี้:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr "ขึ้น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr "ใช้รายการวันทำงาน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "ใช้เพื่อจัดลำดับการคำนวณ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "ใช้เพื่อแสดงกฎเงินเดือนบนสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "Used to display the value in the employer cost dashboard."
msgstr "ใช้เพื่อแสดงค่าในแดชบอร์ดต้นทุนของผู้จ้างงาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_users
msgid "User"
msgstr "ผู้ใช้"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_user
msgid "User can manage all contracts, work entries and create payslips."
msgstr "ผู้ใช้สามารถจัดการสัญญา รายการงาน และสร้างสลิปเงินเดือนทั้งหมดได้"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_manager
msgid "User have full access on the application."
msgstr "ผู้ใช้สามารถเข้าถึงแอปพลิเคชันได้อย่างสมบูรณ์"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr "ตรวจสอบการแก้ไข"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr "ตรวจสอบแล้ว"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr "เวอร์ชั่น"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "View on Employer Cost Dashboard"
msgstr "ดูบนแดชบอร์ดค่าใช้จ่ายของผู้ว่าจ้าง"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payroll_report
msgid "View on Payroll Reporting"
msgstr "ดูการรายงานบัญชีเงินเดือน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "Wage :"
msgstr "ค่าจ้าง :"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__wage_on_payroll
msgid "Wage On Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr "ประเภทค่าแรง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid "Wage indexed by %(percentage).2f%% on %(date)s"
msgstr "จัดทำดัชนีค่าจ้างโดย %(percentage).2f%% เมื่อวันที่ %(date)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "รอ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Warning Color"
msgstr "สีแจ้งเตือน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr "ข้อความเตือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Warning, a similar attachment has been found."
msgstr "คำเตือน พบเอกสารแนบที่คล้ายกัน"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/action_box/action_box.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_warning
msgid "Warnings"
msgstr "คำเตือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry.py:0
msgid ""
"Watch out for gaps in %(employee_name)s's calendar\n"
"\n"
"Please complete the missing work entries of %(employee_name)s:%(time_intervals_str)s \n"
"\n"
"Missing work entries are like the Bermuda Triangle for paychecks. Let's keep your colleague's earnings from vanishing into thin air!"
msgstr ""
"โปรดระวังช่องว่างในปฏิทินของ %(employee_name)s\n"
"\n"
"กรุณากรอกข้อมูลงานที่ขาดหายไปของ %(employee_name)s:%(time_intervals_str)s \n"
"\n"
"มาช่วยกันรักษาข้อมูลรายได้ของเพื่อนร่วมงานของคุณไม่ให้หายไป!"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr "วิธีการปัดเศษประเภทรายการงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "We have to improve our Payroll flow with the new Odoo process"
msgstr "เราต้องปรับปรุงขั้นตอนการจ่ายเงินเดือนด้วยกระบวนการใหม่ของ Odoo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Week %(week_number)s of %(year)s"
msgstr "สัปดาห์ที่ %(week_number)s จาก %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr "รายสัปดาห์"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Weeks %(week)s and %(week1)s of %(year)s"
msgstr "สัปดาห์ที่ %(week)s และ %(week1)s ของปี %(year)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr "เมื่อรายการงานแสดงในสลิปเงินเดือน ค่าจะถูกปัดเศษตามนั้น"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr "สัญญาครั้งก่อนหรือครั้งหน้ามีกำหนดการที่ต่างออกไปหรือไม่"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr "จะใช้เป็นข้อความระบุว่าเหตุใดจึงมีการปรับเปลี่ยนค่าจ้างตามสัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
msgid "Work Days"
msgstr "วันทำงาน"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
msgid "Work Entries"
msgstr "การเข้างาน"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr "วิเคราะห์การเข้างาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr "รายงานการวิเคราะห์การเข้างาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries Export"
msgstr ""

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Work Entries are generated for each <strong>time period</strong> defined in "
"the Working Schedule of the Contract."
msgstr ""
"รายการของงานจะถูกสร้างขึ้นสำหรับ<strong>แต่ละช่วงเวลา</strong>ที่กำหนดไว้ในตารางการทำงานของสัญญา"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries for %(employee)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__work_entry_ids
msgid "Work Entry"
msgstr "การเข้างาน"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr "วิเคราะห์การเข้างาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_employee_mixin
msgid "Work Entry Export Employee"
msgstr "การเข้างาน การส่งออก พนักงาน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_mixin
msgid "Work Entry Export Mixin"
msgstr "การผสมรายการงานส่งออก"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_entry_source
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_source
msgid "Work Entry Source"
msgstr "แหล่งที่มาของรายการงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr "ประเภทการเข้างาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr "ชั่วโมงทำงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr "ระยะเวลาแจ้งการหมดอายุของใบอนุญาตทำงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr "อัตราเวลาทำงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Work entries may not be generated for the period from %(start)s to %(end)s."
msgstr "รายการงานอาจไม่ถูกสร้างขึ้นในช่วงเวลาตั้งแต่ %(start)s ถึง %(end)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "ประเภทการเข้างานสำหรับการลงเวลางานปกติ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate"
msgstr "อัตราเวลาทำงาน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr "อัตราเวลาทำงานเทียบกับตารางการทำงานเต็มเวลาควรอยู่ระหว่าง 0 ถึง 100%"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate versus full time working schedule."
msgstr "อัตราเวลาทำงานเทียบกับตารางการทำงานเต็มเวลา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr "ประเภทงาน"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr "ทำงาน ลางาน (ไม่ได้ค่าจ้าง)"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "วันที่ทำงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "วันที่ทำงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "วันทำงานและป้อนข้อมูล"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr "รายการวันทำงาน"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr "วันทำงานจะไม่ถูกคำนวณ/แสดงในสลิปเงินเดือน"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_working_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__working_rate_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Working Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__calendar
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__calendar
msgid "Working Schedule"
msgstr "ตารางการทำงาน"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_working_schedule_change
msgid "Working Schedule Changes"
msgstr "การเปลี่ยนแปลงตารางการทำงาน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong percentage base or quantity defined for:"
msgstr "กำหนดเปอร์เซ็นต์ฐานหรือปริมาณไม่ถูกต้องสำหรับ:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python code defined for:"
msgstr "โค้ด python ผิดพลาดที่กำหนดไว้สำหรับ:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python condition defined for:"
msgstr "เงื่อนไข python ไม่ถูกต้องสำหรับ:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong quantity defined for:"
msgstr "กำหนดปริมาณไม่ถูกต้องสำหรับ:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong range condition defined for:"
msgstr "กำหนดเงื่อนไขช่วงที่ไม่ถูกต้องสำหรับ:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid ""
"Wrong rule parameter value for %(rule_parameter_name)s at date %(date)s.\n"
"%(error)s"
msgstr ""
"ค่าพารามิเตอร์กฎไม่ถูกต้องสำหรับ %(rule_parameter_name)s ณ วันที่ %(date)s\n"
"%(error)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Wrong warning computation code defined for:\n"
"- Warning: %(warning)s\n"
"- Error: %(error)s"
msgstr ""
"รหัสการคำนวณคำเตือนไม่ถูกต้องที่กำหนดไว้สำหรับ:\n"
"- คำเตือน: %(warning)s\n"
"- ข้อผิดพลาด: %(error)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__ytd
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "YTD"
msgstr "ต้นปี"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "YTD Reset Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_day
msgid "YTD Reset Day of the month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_month
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_month
msgid "YTD Reset Month"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__year
msgid "Year"
msgstr "ปี"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__ytd_computation
msgid "Year to Date Computation"
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_users.py:0
msgid ""
"You are receiving this message because you are the HR Responsible of this "
"employee."
msgstr ""
"คุณได้รับข้อความนี้เนื่องจากคุณเป็นผู้รับผิดชอบด้านทรัพยากรบุคคลของพนักงานรายนี้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You can't delete a batch with payslips if they are not draft or cancelled."
msgstr "คุณไม่สามารถลบชุดสลิปเงินเดือนได้หากไม่ได้มีการร่างหรือถูกยกเลิก"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You can't validate a cancelled payslip."
msgstr "คุณไม่สามารถตรวจสอบสลิปเงินเดือนที่ยกเลิกได้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "You cannot cancel the payment if the payslip has not been paid."
msgstr ""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""
"คุณไม่สามารถลบ %s ได้ เนื่องจากถูกใช้ในโมดูลอื่นแต่คุณสามารถเก็บถาวรแทนได้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr "คุณไม่สามารถลบสลิปเงินเดือนที่ไม่ใช่แบบร่างหรือยกเลิกได้!"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot delete a running salary attachment!"
msgstr "คุณไม่สามารถลบเอกสารแนบเงินเดือนที่ทำงานอยู่ได้!"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot record a payment on multi employees attachments."
msgstr "คุณไม่สามารถบันทึกการชำระเงินในเอกสารแนบของพนักงานหลายคนได้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You cannot reset a batch to draft if some of the payslips have already been "
"paid."
msgstr ""
"คุณไม่สามารถรีเซ็ตชุดงานเป็นฉบับร่างได้หากได้ชำระสลิปเงินเดือนบางส่วนแล้ว"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot validate a payslip on which the contract is cancelled"
msgstr "คุณไม่สามารถตรวจสอบสลิปเงินเดือนที่ยกเลิกสัญญาได้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""
"คุณได้เลือกสัญญาที่ไม่ดำเนินการแล้ว ถ้าคุณต้องการจัดทำดัชนีจริง ๆ "
"โปรดดำเนินการด้วยตัวเอง"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "You must be logged in a %(country_code)s company to use this feature"
msgstr "คุณต้องเข้าสู่ระบบในบริษัท %(country_code)s เพื่อใช้ฟีเจอร์นี้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "You must be logged in a %s company to use this feature"
msgstr "คุณต้องเข้าสู่ระบบในบริษัท %s เพื่อใช้ฟีเจอร์นี้"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "You must select employee(s) to generate payslip(s)."
msgstr "คุณต้องเลือกพนักงานเพื่อสร้างสลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
msgid "You must set a contract to create a payslip line."
msgstr "คุณต้องกำหนดสัญญาเพื่อสร้างรายการสลิปเงินเดือน"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
msgid "You should also be logged into a company in %s to set this country."
msgstr "คุณควรลงชื่อเข้าใช้บริษัทใน %s เพื่อตั้งค่าประเทศนี้ด้วย"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "contracts"
msgstr "สัญญา"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__current_companies_country_codes
msgid "country codes"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr "เช่น เมษายน 2564"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
msgid "e.g. Child Support"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr "เช่น พนักงาน"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
msgid "e.g. Employee Without Contracts"
msgstr "เช่น พนักงานที่ไม่มีสัญญา"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr "เช่น สุทธิ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr "เช่น เงินเดือนสุทธิ"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr "เช่น การจ่ายปกติ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__headcount_id
msgid "headcount_id"
msgstr "headcount_id"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"
msgstr "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr "ของ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "ผลลัพธ์จะส่งผลต่อตัวแปร"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "xxxxxxxxxxxx"
msgstr "xxxxxxxxxxxx"

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr "{{ object.employee_id.name }} มีสลิปเงินเดือนใหม่พร้อมให้คุณ"
