# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest_enterprise
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_0
msgid "<b class=\"tip_title\">Tip: Navigate menus like a pro</b>"
msgstr "<b class=\"tip_title\">ヒント: プロのようにメニューをナビゲート</b>"

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_1
msgid ""
"Connect your WhatsApp account to Odoo to receive inbound messages directly "
"in your CRM."
msgstr "WhatsAppアカウントをOdooに接続すると、CRMに直接着信メッセージを受信できます。"

#. module: digest_enterprise
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_0
msgid ""
"From the Home page, start typing what you would like to access. Odoo will "
"suggest menus matching your search. Use arrow keys and press enter to jump "
"to your menu."
msgstr ""
"トップページからアクセスしたい項目を入力してください。Odooが検索条件に合ったメニューを提案します。矢印キーを押しながらEnterキーを押すとメニューにジャンプします。"

#. module: digest_enterprise
#: model:digest.tip,name:digest_enterprise.digest_tip_web_enterprise_0
msgid "Tip: Navigate menus like a pro"
msgstr "ヒント: プロのようにメニューをナビゲート"

#. module: digest_enterprise
#: model:digest.tip,name:digest_enterprise.digest_tip_web_enterprise_1
#: model_terms:digest.tip,tip_description:digest_enterprise.digest_tip_web_enterprise_1
msgid "Tip: Your Business right in their pocket"
msgstr "ヒント:あなたのビジネスを顧客のポケットの中に"
