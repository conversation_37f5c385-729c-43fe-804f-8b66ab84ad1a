# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_maintenance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2019-08-26 09:37+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"Language: lb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "<span class=\"o_stat_text\">Maintenance</span>"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__company_id
msgid "Company"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr ""

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture1
msgid "Crosscut Saw: 8 ppi."
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Discard"
msgstr ""

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture3
msgid "Drill Machine"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Equipment"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_form_inherit_maintenance
msgid "Est. Next Failure"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Estimated Next Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__expected_mtbf
msgid "Expected MTBF"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__expected_mtbf
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Expected Mean Time Between Failure"
msgstr ""

#. module: mrp_maintenance
#: model:maintenance.equipment.category,name:mrp_maintenance.equipment_furniture_tools
msgid "Furniture Tools"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Latest Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__latest_failure_date
msgid "Latest Failure Date"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__mtbf
msgid "MTBF"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__mttr
msgid "MTTR"
msgstr ""

#. module: mrp_maintenance
#: model:ir.ui.menu,name:mrp_maintenance.menu_equipment_dashboard
msgid "Machines & Tools"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_workcenter_view_kanban_inherit_maintenance
msgid "Maintenance"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_maintenance_equipment
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_workcenter__equipment_ids
msgid "Maintenance Equipment"
msgstr ""

#. module: mrp_maintenance
#. odoo-javascript
#: code:addons/mrp_maintenance/static/src/components/menuPopup.xml:0
#: model:ir.model,name:mrp_maintenance.model_maintenance_request
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.mrp_production_view_form_inherit_maintenance
msgid "Maintenance Request"
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
msgid "Maintenance Requests"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_id
msgid "Manufacturing Order"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Mean Time Between Failure"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__mtbf
msgid "Mean Time Between Failure, computed based on done corrective maintenances."
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,help:mrp_maintenance.field_maintenance_equipment__mttr
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Mean Time To Repair"
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
msgid "New Maintenance Request"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__maintenance_count
msgid "Number of maintenance requests"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_search_inherit_mrp
msgid "Operation"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__production_company_id
msgid "Production Company"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_production
msgid "Production Order"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model.fields,field_description:mrp_maintenance.field_mrp_production__request_ids
msgid "Request"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_request_view_form_inherit_mrp
msgid "Save"
msgstr ""

#. module: mrp_maintenance
#: model:maintenance.equipment,name:mrp_maintenance.equipment_furniture2
msgid "Scrub Plane"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_equipment__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "Work Center"
msgstr ""

#. module: mrp_maintenance
#: model:ir.ui.menu,name:mrp_maintenance.menu_workcenter_tree
msgid "Work Centers"
msgstr ""

#. module: mrp_maintenance
#: model:ir.model,name:mrp_maintenance.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp_maintenance.field_maintenance_request__workorder_id
msgid "Work Order"
msgstr ""

#. module: mrp_maintenance
#: model_terms:ir.ui.view,arch_db:mrp_maintenance.maintenance_equipment_view_form_inherit_mrp
msgid "days"
msgstr ""

#. module: mrp_maintenance
#. odoo-python
#: code:addons/mrp_maintenance/models/mrp_maintenance.py:0
msgid "work centers"
msgstr ""
