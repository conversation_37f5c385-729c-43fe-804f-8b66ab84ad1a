# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_holidays
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
msgid "%(names)s are on time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
msgid "%(names)s is on time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
msgid "%(names)s requested time off %(leaves)s. \n"
msgstr ""

#. module: project_holidays
#: model_terms:ir.ui.view,arch_db:project_holidays.view_task_form2_inherit_holidays
msgid "<i class=\"fa fa-user-times me-2\" role=\"img\" title=\"Leave warning\"/>"
msgstr ""

#. module: project_holidays
#. odoo-python
#: code:addons/project_holidays/models/project.py:0
msgid "Operation not supported"
msgstr "Operace není podporována"

#. module: project_holidays
#: model:ir.model,name:project_holidays.model_project_task
msgid "Task"
msgstr "Úkol"
