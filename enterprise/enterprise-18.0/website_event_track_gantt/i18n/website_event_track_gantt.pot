# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_gantt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Proposed By — </strong>"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "<strong>Start — </strong>"
msgstr ""

#. module: website_event_track_gantt
#. odoo-javascript
#: code:addons/website_event_track_gantt/static/src/event_track_form_view.js:0
msgid "Are you sure you want to delete this track?"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Delete"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Discard"
msgstr ""

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_event
msgid "Event"
msgstr ""

#. module: website_event_track_gantt
#: model:ir.model,name:website_event_track_gantt.model_event_track
msgid "Event Track"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Save"
msgstr ""

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_initial_date
msgid "Track Gantt Initial Date"
msgstr ""

#. module: website_event_track_gantt
#: model:ir.model.fields,field_description:website_event_track_gantt.field_event_event__track_gantt_scale
msgid "Track Gantt Scale"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_gantt
msgid "Tracks"
msgstr ""

#. module: website_event_track_gantt
#: model_terms:ir.ui.view,arch_db:website_event_track_gantt.event_track_view_form_in_gantt
msgid "Unschedule"
msgstr ""
