# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_dimona
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 08:49+0000\n"
"PO-Revision-Date: 2025-02-07 17:07+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.actions.server,name:l10n_be_hr_payroll_dimona.ir_cron_check_dimona_ir_actions_server
msgid "Belgian Payroll: Check DIMONA"
msgstr "Paie belge : Vérifier DIMONA"

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__cancel
msgid "Cancel employee declaration"
msgstr "Annuler la déclaration de l'employé"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Cannot connect with the ONSS servers. Please contact an administrator. (%s)"
msgstr ""
"Impossible de se connecter aux serveurs de l'ONSS. Veuillez contacter un "
"administrateur. (%s)"

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.hr_contract_view_form
msgid "Check Dimona"
msgstr "Vérifier Dimona"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_id
msgid "Contract"
msgstr "Contrat"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_country_code
msgid "Country Code"
msgstr "Code du pays"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll_dimona.menu_l10n_be_dimona_wizard
msgid "DIMONA"
msgstr "DIMONA"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA Cancel declaration posted successfully, waiting validation"
msgstr ""
"Annuler la déclaration DIMONA envoyée avec succès, en attente de validation"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA IN declaration posted successfully, waiting validation"
msgstr "Déclaration DIMONA IN envoyée avec succès, en attente de validation"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA Out declaration posted successfully, waiting validation"
msgstr "Déclaration DIMONA OUT envoyée avec succès, en attente de validation"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA Update declaration posted successfully, waiting validation"
msgstr ""
"Mise à jour de la déclaration DIMONA envoyée avec succès, en attente de "
"validation"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"DIMONA declaration treated and accepted with non blocking anomalies\n"
"%(anomalies)s\n"
"%(informations)s"
msgstr ""
"Déclaration DIMONA traitée et acceptée avec des anomalies non bloquantes\n"
"%(anomalies)s\n"
"%(informations)s"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA declaration treated and accepted without anomalies"
msgstr "Déclaration DIMONA traitée et acceptée sans anomalies"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"DIMONA declaration treated and refused (blocking anomalies)\n"
"%s"
msgstr ""
"Déclaration DIMONA traitée et refusée (anomalies bloquantes)\n"
"%s"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "DIMONA declaration waiting worker identification by Sigedis"
msgstr ""
"Déclaration DIMONA en attente de l'identification du travailleur par Sigedis"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__employee_birthday
msgid "Date of Birth"
msgstr "Date de naissance"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__declaration_type
msgid "Declaration Type"
msgstr "Type de déclaration"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__done
msgid "Declared and accepted"
msgstr "Déclarée et acceptée"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__done_warning
msgid "Declared and accepted with warnings"
msgstr "Déclarée et acceptée avec avertissements"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__refused
msgid "Declared and refused"
msgstr "Déclarée et refusée"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__waiting_sigedis
msgid "Declared and waiting Sigedis"
msgstr "Déclarée et en attente de Sigedis"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__waiting
msgid "Declared and waiting status"
msgstr "Déclarée et en attente du statut"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_l10n_be_dimona_wizard
msgid "Dimona Wizard"
msgstr "Assistant Dimona"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Due to a technical problem at the ONSS side, the Dimona declaration could "
"not be received by the ONSS."
msgstr ""
"En raison d'un problème technique du côté de l'ONSS, la déclaration Dimona "
"n'a pas pu être envoyée à l'ONSS."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Due to a technical problem at the ONSS side, the authentication could not be"
" done by the ONSS."
msgstr ""
"En raison d'un problème technique du côté de l'ONSS, l'authentification n'a "
"pas pu être effectuée par l'ONSS."

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__employee_id
msgid "Employee"
msgstr "Employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_hr_contract
msgid "Employee Contract"
msgstr "Contrat de l'employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__without_niss
msgid "Employee Without NISS"
msgstr "Employé sans NISS"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_end
msgid "End Date"
msgstr "Date de fin"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,help:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "Date de fin du contrat (s'il s'agit d'un contrat à durée déterminée)."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "Error on authentication. Please contact an administrator. (%s)"
msgstr ""
"Erreur lors de l'authentification. Veuillez contacter un administrateur. "
"(%s)"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Error with one or several invalid parameters on the POST request during "
"authentication. Please contact an administrator. (%s)"
msgstr ""
"Erreur due à un ou plusieurs paramètres invalides dans la requête POST "
"pendant l'authentification. Veuillez contacter un administrateur. (%s)"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Error with one or several invalid parameters on the POST request. Please "
"contact an administrator. (%s)"
msgstr ""
"Erreur due à un ou plusieurs paramètres invalides dans la requête POST. "
"Veuillez contacter un administrateur. (%s)"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Foreigner employees should provide a complete address (street, number, zip, "
"city, country"
msgstr ""
"Les employés étrangers doivent fournir une adresse complète (rue, numéro, "
"code postal, ville, pays"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Foreigner employees should provide their name, birthdate, birth place, birth"
" country, nationality and the gender"
msgstr ""
"Les employés étrangers doivent fournir leur nom, date de naissance, lieu de "
"naissance, ville de naissance, nationalité et sexe"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__error
msgid "Invalid declaration or restricted access"
msgstr "Déclaration invalide ou accès restreint"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_declaration_state
msgid "L10N Be Dimona Declaration State"
msgstr "L10N Be Statut déclaration Dimona"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_in_declaration_number
msgid "L10N Be Dimona In Declaration Number"
msgstr "L10N Be Numéro déclaration Dimona In"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_last_declaration_number
msgid "L10N Be Dimona Last Declaration Number"
msgstr "L10N Be Numéro dernière déclaration Dimona"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_is_student
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_is_student
msgid "L10N Be Is Student"
msgstr "L10N Be Est étudiant"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Manage DIMONA"
msgstr "Gérer DIMONA"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "No Certificate definer on the Payroll Configuration"
msgstr "Pas de spécification de certificat dans la configuration de la paie"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "No DIMONA declaration is linked to this contract"
msgstr "Aucune déclaration DIMONA n'est liée à ce contrat"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "No ONSS registration number is defined for company %s"
msgstr ""
"Aucun numéro d'enregistrement auprès de l'ONSS n'est défini pour la société "
"%s"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "No expeditor number defined on the payroll settings."
msgstr ""
"Aucun numéro d'expéditeur n'est défini dans les paramètres de la paie."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "No house number found on employee street"
msgstr "Aucun numéro de maison n'est trouvée dans la rue de l'employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__none
msgid "Not Declared"
msgstr "Non déclarée"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__out
msgid "Register employee departure"
msgstr "Enregistrer le départ de l'employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__in
msgid "Register employee entrance"
msgstr "Enregistrer l'arrivée de l'employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_start
msgid "Start Date"
msgstr "Date de début"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "Start date and end date should belong to the same quarter."
msgstr ""
"La date de début et la date de fin doivent appartenir au même trimestre."

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_planned_hours
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_planned_hours
msgid "Student Planned Hours"
msgstr "Heures planifiées de l'étudiant"

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Submit Declaration"
msgstr "Soumettre la déclaration"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "The DIMONA should be introduced before start date for students."
msgstr "La DIMONA doit être introduite avant la date de début des étudiants."

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,help:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "The NISS is invalid."
msgstr "Le NISS est invalide."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"The declaration has been submitted but not processed yet or the declaration "
"reference is not known. (%s)"
msgstr ""
"La déclaration a été soumise, mais n'est pas encore traitée ou la référence "
"de la déclaration n'est pas connue. (%s)"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "The employee name is incomplete"
msgstr "Le nom de l'employé est incomplet"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "The employee zip does not exist."
msgstr "Le code postal de l'employé n'existe pas."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "There is already a IN declaration for this contract."
msgstr "Il y a déjà une déclaration IN pour ce contrat."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "There is no contract defined on the employee form."
msgstr "Aucun contrat n'est défini sur la fiche de l'employé."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "There is no defined end date on the student contract."
msgstr "Aucune date de fin n'est définie sur le contrat de l'étudiant."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "There is no defined planned hours on the student contract."
msgstr ""
"Il n'y a pas d'heures planifiées définies sur le contrat de l'étudiant."

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "There is not end date defined on the employee contract."
msgstr "Aucune date de fin n'est définie sur le contrat de l'employé."

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__update
msgid "Update employee information"
msgstr "Mettre à jour les informations de l'employé"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_wage_type
msgid "Wage Type"
msgstr "Type de salaire"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid "You don't have the right to call this action"
msgstr "Vous n'avez pas le droit d'appeler cette action"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Vous devez être connecté à une entreprise belge pour utiliser cette "
"fonctionnalité"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Your user does not have the rights to consult this declaration. This "
"happens, for example, if the user does not have or no longer has a mandate "
"for the employer. (%s)"
msgstr ""
"Votre utilisateur n'a pas le droit de consulter cette déclaration. C'est le "
"cas, par exemple, si l'utilisateur n'a pas ou plus de mandat pour "
"l'employeur. (%s)"

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
msgid ""
"Your user does not have the rights to make a declaration for the employer. "
"This happens, for example, if the user does not have or no longer has a "
"mandate for the employer. (%s)"
msgstr ""
"Votre utilisateur n'a pas le droit d'effectuer une déclaration pour "
"l'employeur. C'est le cas, par exemple, si l'utilisateur n'a pas ou plus de "
"mandat pour l'employeur. (%s)"
