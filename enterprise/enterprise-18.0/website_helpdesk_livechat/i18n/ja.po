# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_livechat
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script_step.py:0
msgid "%(name)s's Ticket"
msgstr "%(name)sのチケット"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_done
msgid "Alright, we should have everything we need"
msgstr "さて、全て必要なものは揃いました"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script.py:0
msgid ""
"An \"Email\" step type must exist before the \"Create Ticket\" step for a "
"ticket to be created."
msgstr "チケットを作成するには、\"チケット作成t\" ステップの前に\"Eメール\" ステップタイプが存在する必要があります。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_misc
msgid "Anything else to add?"
msgstr "他に追加するものはありますか？"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "チャットボットスクリプト"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "チャットボットスクリプト ステップ"

#. module: website_helpdesk_livechat
#: model:ir.ui.menu,name:website_helpdesk_livechat.chatbot_config
msgid "Chatbots"
msgstr "チャットボット"

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Chatbots"
msgstr "チャットボット設定"

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Live Chat Channel"
msgstr "ライブチャットチャンネルを設定"

#. module: website_helpdesk_livechat
#: model:ir.model.fields.selection,name:website_helpdesk_livechat.selection__chatbot_script_step__step_type__create_ticket
msgid "Create Ticket"
msgstr "チケット作成"

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/core/web/store_service_patch.js:0
msgid "Create a new helpdesk ticket (/ticket ticket title)"
msgstr "新規ヘルプデスクチケットを作成 (/チケット チケットタイトル)"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Create a new helpdesk ticket by typing: %(pre_start)s%(ticket_command)s "
"%(i_start)sticket title%(i_end)s%(pre_end)s"
msgstr ""
"次を入力して新規ヘルプデスクチケットを作成: %(pre_start)s%(ticket_command)s "
"%(i_start)sチケットタイトル%(i_end)s%(pre_end)s"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Created a new ticket: %s"
msgstr "新規チケットを作成済: %s"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "ディスカッションチャンネル"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch
msgid "First, what is the nature of your issue?"
msgstr "まずはじめに、問題はどういったものでしょう？"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_yes
msgid "Great, that will make our lives easier."
msgstr "素晴らしい、助かります。"

#. module: website_helpdesk_livechat
#: model:chatbot.script,title:website_helpdesk_livechat.chatbot_script_helpdesk_bot
msgid "Helpdesk Bot"
msgstr "ヘルプデスクボット"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__helpdesk_team_id
msgid "Helpdesk Team"
msgstr "ヘルプデスクチーム"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_welcome
msgid "Here we go, help is on the way!"
msgstr "まもなくサポートさせて頂きます。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical
msgid "I have a technical issue"
msgstr "技術的な問題です。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative
msgid "I have an administrative question"
msgstr "事務手続きについて質問です。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_no
msgid "It's OK, we can also find your contract by other means."
msgstr "問題ありません。別の方法でお客様の契約書を見つけることもできます。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_email
msgid "Just a last thing, can we please have your email address?"
msgstr "最後に、メールアドレスをお伺いできますか？"

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr "Webチャット"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Load More"
msgstr "さらにロード"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_no
msgid "No"
msgstr "いいえ"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"No tickets found for %(b_start)s%(keywords)s%(b_end)s.%(br)sMake sure you "
"are using the right format: %(pre_start)s%(search_tickets_command)s "
"%(i_start)skeywords%(i_end)s%(pre_end)s"
msgstr ""
"%(b_start)s%(keywords)s%(b_end)s用のチケットが見つかりません。%(br)s正しいフォーマットを使用しているかご確認下さい:"
" %(pre_start)s%(search_tickets_command)s "
"%(i_start)sキーワード%(i_end)s%(pre_end)s"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_ticket
msgid ""
"OK, I just created a ticket for you. You should receive an email "
"confirmation very soon."
msgstr "今、チケットを作成しました。まもなく確認メールが届きます。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial
msgid "Please write below the serial number of your equipment."
msgstr "お手持ちの機器のシリアル番号をご記入下さい。"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_input
msgid "Please write below your customer reference."
msgstr "お客様番号をご記入下さい。"

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/core/web/store_service_patch.js:0
msgid "Search helpdesk tickets (/search_tickets keyword)"
msgstr "ヘルプデスクチケットを検索 (/search_tickets keyword)"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Search helpdesk tickets by typing: %(pre_start)s%(search_tickets_command)s "
"%(i_start)skeywords%(i_end)s%(pre_end)s"
msgstr ""
"次を入力してヘルプデスクチケットを検索: %(pre_start)s%(search_tickets_command)s "
"%(i_start)sキーワード%(i_end)s%(pre_end)s"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Something is missing or wrong in command"
msgstr "コマンドに何かが足りないか、間違っています"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Something is missing or wrong in the command"
msgstr "コマンドに何かが足りないか、間違っています"

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "ステップタイプ"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_thanks
msgid "Thank you, that will help our engineers see what went wrong."
msgstr "ありがとうございます。何が問題だったのか、エンジニアが理解するのに役立たせて頂きます。"

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.chatbot_script_view_form
msgid "Tickets"
msgstr "チケット"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Tickets search results for %(b_start)s%(keywords)s%(b_end)s: "
"%(br)s%(tickets)s"
msgstr "%(b_start)s%(keywords)s%(b_end)s: %(br)s%(tickets)s用のチケット検索結果"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref
msgid ""
"To start with, do you have a customer reference?\n"
"They are written on each invoice you received, next to your name."
msgstr ""
"はじめに、お客様番号はお持ちですか？\n"
"お客様番号は、請求書の名前の横に書かれています。"

#. module: website_helpdesk_livechat
#: model:res.groups,name:website_helpdesk_livechat.group_use_website_helpdesk_livechat
msgid "Use Live Chat"
msgstr "ライブチャットを使用"

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_issue
msgid "We're all set. Now, what is your issue?"
msgstr "用意ができました。何にお困りですか？"

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_yes
msgid "Yes"
msgstr "はい"
