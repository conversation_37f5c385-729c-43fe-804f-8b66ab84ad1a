# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_invoice_extract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_ir_attachment
msgid "Attachment"
msgstr "Vedhæftning"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_banners
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_banners
msgid "Can show the ocr banners"
msgstr "Kan vise ocr bannerne"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Kan vise ocr send knappen"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "Couldn't reload AI data."
msgstr "Kunne ikke genindlæse AI data."

#. module: account_invoice_extract
#. odoo-javascript
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_form_renderer.js:0
msgid "Create"
msgstr "Opret"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_out_invoice_digitalization_mode
msgid "Customer Invoices"
msgstr "Kundefakturaer"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_out_invoice_digitalization_mode
msgid "Digitization mode on customer invoices"
msgstr "Digitaliseringstilstand på kunde fakturaer"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_in_invoice_digitalization_mode
msgid "Digitization mode on vendor bills"
msgstr "Digitaliseringstilstand på kreditor regninger"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__auto_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr "Digitaliser automatisk"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Digitize document"
msgstr "Digitalisere dokument"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__manual_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__manual_send
msgid "Digitize on demand only"
msgstr "Digitaliser kun efter behov"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__no_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__no_send
msgid "Do not digitize"
msgstr "Må ikke digitaliseres"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Enable to get only one invoice line per tax"
msgstr "Slå til for at modtage kun én faktura linje per afgift"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_error_message
msgid "Error message"
msgstr "Fejl besked"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_attachment_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_attachment_id
msgid "Extract Attachment"
msgstr "Udtræk vedhæftning"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_detected_layout
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_detected_layout
msgid "Extract Detected Layout Id"
msgstr "Udtræk detekteret layout-id"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_partner_name
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_partner_name
msgid "Extract Detected Partner Name"
msgstr "Udtræk detekteret partnernavn"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_prefill_data
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_prefill_data
msgid "Extract Prefill Data"
msgstr "Udtræk forudfyldningsdata"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state_processed
msgid "Extract State Processed"
msgstr "Udtræks status behandlet"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_word_ids
msgid "Extract Word"
msgstr "Udtræk ord"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state
msgid "Extract state"
msgstr "Udtræk tilstand"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_status
msgid "Extract status"
msgstr "Udtræks status"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr "Udtrukne ord fra faktura scanning"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Extraction Information"
msgstr "Udtræknings information"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr "Felt"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr "ID"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID på anmodning til IAP-OCR'en"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr "Faktura"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Invoice OCR: Update All Status"
msgstr "Faktura OCR: Opdaer alle status"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Invoice OCR: Validate Invoices"
msgstr "Faktura OCR: Validere fakturaer"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Er i udtræks status"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_move
msgid "Journal Entry"
msgstr "Journalbilag"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_main_attachment_id
msgid "Main Attachment"
msgstr "Vedhæftning"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__ocr_selected
msgid "Ocr Selected"
msgstr "Ocr valgt"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Reload AI Data"
msgstr "Genindlæs AI data"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.model_account_send_for_digitalization
msgid "Send Bills for digitization"
msgstr "Send regninger til digitalisering"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_single_line_per_tax
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_single_line_per_tax
msgid "Single Invoice Line Per Tax"
msgstr "En faktura linje per afgift"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr "Bruge valgte"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_in_invoice_digitalization_mode
msgid "Vendor Bills"
msgstr "Leverandørfakturaer"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__website_message_ids
msgid "Website communication history"
msgstr "Hjemmesidens kommunikations historik"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr "Ord boks vinkel"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr "Ord boks højde"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr "Ord box midt x"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr "Ord boks midt y"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr "Ord boks højde"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr "Ord side"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr "Ord tekst"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "Du kan ikke sende en udgift der ikke er i kladde tilstand!"
