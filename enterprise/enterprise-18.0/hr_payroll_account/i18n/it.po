# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_bank_statement_line__payslip_count
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_move__payslip_count
msgid "# of Payslips"
msgstr "N. buste paga"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"move_state != 'draft'\">Journal Entry <br/>(Draft)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'posted'\">Journal Entry <br/>(Posted)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'cancel'\">Journal Entry <br/>(Canceled)</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"move_state != 'draft'\">Registrazione contabile <br/>(bozza)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'posted'\">Registrazione contabile <br/>(pubblicata)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'cancel'\">Registrazione contabile <br/>(annullata)</span>"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Payslip</span>"
msgstr "<span class=\"o_stat_text\">Busta paga</span>"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modello piano dei conti"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Account Payslip Houserental"
msgstr "Conto busta paga affitto casa"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "Contabilità"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Voce contabile"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "Adjustment Entry"
msgstr "Registrazione di rettifica"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Conto analitico"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed?"
msgstr "Sei sicuro di voler procedere?"

#. module: hr_payroll_account
#: model:ir.actions.act_window,name:hr_payroll_account.action_res_partner_bank_account_form
#: model:ir.model,name:hr_payroll_account.model_res_partner_bank
#: model:ir.ui.menu,name:hr_payroll_account.menu_hr_employee_bank_account
msgid "Bank Accounts"
msgstr "Conti bancari"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.res_config_settings_view_form
msgid "Batch Account Move Lines"
msgstr "Gruppo righe movimento contabile"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__batch_payroll_move_lines
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_company__batch_payroll_move_lines
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_config_settings__batch_payroll_move_lines
msgid "Batch Payroll Move Lines"
msgstr "Gruppo righe movimento libro paga"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr "Cronologia contratto"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "Crea voce in bozza"

#. module: hr_payroll_account
#: model_terms:ir.actions.act_window,help:hr_payroll_account.action_res_partner_bank_account_form
msgid "Create a Bank Account"
msgstr "Creazione di un conto corrente bancario"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Conto Credito"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_line__credit_tag_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__credit_tag_ids
msgid "Credit Tax Grids"
msgstr "Griglie imposte di credito"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "Data Conto"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Conto Debito"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_line__debit_tag_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__debit_tag_ids
msgid "Debit Tax Grids"
msgstr "Griglie imposte di debito"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "Contratto dipendente"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_partner_bank_search_inherit
msgid "Employees"
msgstr "Dipendenti"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_res_config_settings__batch_payroll_move_lines
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.res_config_settings_view_form
msgid ""
"Enable this option to merge all the accounting entries for the same period "
"into a single account move line. This will anonymize the accounting entries "
"but also disable single payment generations."
msgstr ""
"Attiva quest'opzione per unire tutte le registrazioni contabili dello stesso"
" periodo in un'unica riga di movimento del conto. Questo renderà anonime le "
"registrazioni contabili ma disabiliterà anche le generazioni di pagamenti "
"singoli."

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__split_move_lines
msgid ""
"Enable this option to split the accountig entries for this rule according to"
" the payslip line name. It could be useful for deduction/reimbursement or "
"salary attachments for instance."
msgstr ""
"Abilita l'opzione per dividere le registrazioni contabili per la regola "
"secondo il nome della riga della busta paga. Potrebbe essere utile per "
"detrazioni/rimborsi o allegati dello stipendio."

#. module: hr_payroll_account
#: model_terms:ir.actions.act_window,help:hr_payroll_account.action_res_partner_bank_account_form
msgid ""
"From here you can manage all bank accounts linked to you and your contacts."
msgstr ""
"Da qui puoi gestire tutti i conti bancari collegati a te e ai tuoi contatti."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "Procedura guidata resoconto pagamenti HR"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_setup_bank_manual_config__has_alt_bank_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_partner_bank__has_alt_bank_account
msgid "Has Alt Bank Account"
msgstr "Ha conto bancario alternativo"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_structure.py:0
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr ""
"Giornale errato: Il giornale deve essere nella stessa valuta dell'azienda"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Invalid IBAN for the following employees:\n"
"%s"
msgstr ""
"IBAN non valido per i seguenti dipendenti:\n"
"%s"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_journal
msgid "Journal"
msgstr "Registro"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
#: code:addons/hr_payroll_account/models/hr_payslip_run.py:0
#: model:ir.model,name:hr_payroll_account.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""
"Tenere vuoto per utilizzare il periodo della data di validazione (busta "
"paga)"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_run__move_id
msgid "Move"
msgstr "Movimento"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_run__move_state
msgid "Move State"
msgstr "Stato movimento"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr "Non computati nel netto contabile"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "One of the contract for these payslips has no structure type."
msgstr "Uno dei contratti di queste buste paga non ha un tipo di struttura."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "One of the payroll structures has no account journal defined on it."
msgstr "Nessun registro contabile definito in una delle strutture libro paga."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Pay"
msgstr "Paga"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "Busta paga"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/wizard/account_payment_register.py:0
msgid "Payment done at %s"
msgstr "Pagamento effettuato a %s"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_payment
msgid "Payments"
msgstr "Pagamenti"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Batch busta paga"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip_line
msgid "Payslip Line"
msgstr "Riga Busta Paga"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_bank_statement_line__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_move__payslip_ids
msgid "Payslips"
msgstr "Buste paga"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "SLR"
msgstr "SLR"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Salaries"
msgstr "Stipendi"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "Giornale stipendi"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Regola di retribuzione"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "Struttura stipendio"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__split_move_lines
msgid "Split account line based on name"
msgstr "Dividi riga conto in base al nome"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip_line__credit_tag_ids
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__credit_tag_ids
msgid ""
"Tags assigned to this line will impact financial reports when translated "
"into an accounting journal entry.They will be applied on the credit account "
"line in the journal entry."
msgstr ""
"I tag assegnati a questa riga incideranno sui rendiconti finanziari al "
"momento della traduzione in voce di registro. Verranno applicati alla riga "
"del conto di credito nella registrazione contabile. "

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip_line__debit_tag_ids
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__debit_tag_ids
msgid ""
"Tags assigned to this line will impact financial reports when translated "
"into an accounting journal entry.They will be applied on the debit account "
"line in the journal entry."
msgstr ""
"I tag assegnati a questa riga incideranno sui rendiconti finanziari al "
"momento della traduzione in voce di registro. Verranno applicati alla riga "
"del conto di debito nella registrazione contabile. "

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid ""
"The Expense Journal \"%s\" has not properly configured the default Account!"
msgstr ""
"Il giornale delle spese \"%s\" non possiede un conto configurato "
"correttamente!"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "The credit account on the NET salary rule is not reconciliable"
msgstr ""
"Il conto di credito presente nella regola dello stipendio NETTO non è "
"riconciliabile"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "The employee bank account is untrusted"
msgstr "Il conto bancario del dipendente non è affidabile"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
"Il campo consente di eliminare il valore di questa regola nella regola "
"\"Retribuzione netta\" a livello contabile, per visualizzare in modo "
"esplicito il valore stesso nella contabilità. Ad esempio, selezionare il "
"campo per visualizzare il valore degli oneri di rappresentanza. "

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_partner_bank_form_inherit_hr_payroll_account
msgid "This will replace the employee existing bank account."
msgstr "Questo sostituirà il conto bancario esistente del dipendente."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "You can only register payment for posted journal entries."
msgstr ""
"È possibile registrare pagamenti solo per registrazioni contabili "
"confermate."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "You can only register payments for unpaid documents."
msgstr "È possibile registrare pagamenti solo per documenti non pagati."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_journal.py:0
msgid "You cannot delete the journal linked to a Salary Structure"
msgstr ""
"Non è possibile eliminare il registro collegato alla struttura dello "
"stipendio"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "You can't create a journal entry for a paid payslip."
msgstr ""
"Non puoi creare una registrazione contabile per una busta paga pagata."
