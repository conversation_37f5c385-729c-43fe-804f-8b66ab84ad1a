# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_ups
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "(<PERSON> My Account)"
msgstr "（記賬至我的帳戶）"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "<i class=\"fa fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/>"
msgstr "<i class=\"fa fa-trash-o\" role=\"img\" aria-label=\"刪除\" title=\"刪除\"/>"

#. module: website_sale_ups
#: model_terms:payment.provider,pending_msg:website_sale_ups.payment_provider_ups_cod
msgid ""
"<i>Pending</i>, Thanks for choosing COD(Collect on Delivery/Cash on "
"Delivery) option. Delivery boy will collect the payment on delivery."
msgstr "<i>待定</i>，感謝您選擇 COD（貨到付款/貨到付款）選項。送貨員將收取貨到付款。"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "<span>(UPS Billing will remain to the customer)</span>"
msgstr "<span>（UPS 賬單仍保留給客戶）</span>"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Apply"
msgstr "套用"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Cancel"
msgstr "取消"

#. module: website_sale_ups
#: model:ir.model.fields.selection,name:website_sale_ups.selection__payment_provider__custom_mode__cash_on_delivery
msgid "Cash On Delivery"
msgstr "貨到現金付款"

#. module: website_sale_ups
#: model:payment.method,name:website_sale_ups.payment_method_cash_on_delivery
#: model:payment.provider,name:website_sale_ups.payment_provider_ups_cod
msgid "Cash on Delivery"
msgstr "貨到現金付款"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Close"
msgstr "關閉"

#. module: website_sale_ups
#: model:ir.model.fields,field_description:website_sale_ups.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "自訂模式"

#. module: website_sale_ups
#: model:ir.model,name:website_sale_ups.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: website_sale_ups
#: model:ir.model,name:website_sale_ups.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "UPS Bill My Account"
msgstr "UPS 記賬至我的帳戶"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.property_ups_carrier_account_inherit_portal_details
msgid "UPS Number Account"
msgstr "UPS 號碼帳戶"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.res_config_settings_view_form
msgid "UPS Shipping Methods"
msgstr "UPS Shipping Methods"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_delivery_method
msgid "Using Account"
msgstr "使用帳戶"

#. module: website_sale_ups
#: model_terms:ir.ui.view,arch_db:website_sale_ups.ups_bill_my_account_dialog
msgid "Your UPS Account Number"
msgstr "您的 UPS 帳號"

#. module: website_sale_ups
#: model_terms:payment.provider,auth_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: website_sale_ups
#: model_terms:payment.provider,cancel_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: website_sale_ups
#: model_terms:payment.provider,done_msg:website_sale_ups.payment_provider_ups_cod
msgid "Your payment has been successfully processed."
msgstr "你的付款已成功處理。"

#. module: website_sale_ups
#. odoo-python
#: code:addons/website_sale_ups/models/payment_provider.py:0
msgid "no UPS carriers available"
msgstr "沒有 UPS 承運商可用"
