# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_social
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_event_track_social
#: model_terms:ir.ui.view,arch_db:website_event_track_social.event_track_view_form
msgid "<span invisible=\"not push_reminder\">minutes before start</span>"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,help:website_event_track_social.field_event_track__push_reminder
msgid ""
"Check this if you want to send a push notification reminder to everyone that"
" has favorited this track."
msgstr ""

#. module: website_event_track_social
#: model_terms:ir.ui.view,arch_db:website_event_track_social.event_track_view_form
msgid "Edit Push Reminder"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_event_track__firebase_enable_push_notifications
msgid "Enable Web Push Notifications"
msgstr ""

#. module: website_event_track_social
#: model:ir.model,name:website_event_track_social.model_event_track
msgid "Event Track"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,help:website_event_track_social.field_event_track__push_reminder_delay
msgid ""
"How many minutes before the start of the talk do you want to send the "
"reminder?"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_social_post__event_track_id
msgid "Linked Event Track"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_website_visitor__event_track_push_enabled_ids
msgid "Push Enabled Tracks"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_event_track__push_reminder
#: model_terms:ir.ui.view,arch_db:website_event_track_social.event_track_view_form
msgid "Push Reminder"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_event_track__push_reminder_delay
msgid "Push Reminder Delay"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,field_description:website_event_track_social.field_event_track__push_reminder_posts
msgid "Push Reminders"
msgstr ""

#. module: website_event_track_social
#: model:ir.model,name:website_event_track_social.model_social_post
msgid "Social Post"
msgstr ""

#. module: website_event_track_social
#. odoo-python
#: code:addons/website_event_track_social/models/event_track.py:0
msgid "There are no push reminders associated with this track"
msgstr ""

#. module: website_event_track_social
#: model:ir.model.fields,help:website_event_track_social.field_website_visitor__event_track_push_enabled_ids
msgid ""
"Tracks that are 'default favorited' can be blacklisted and the visitor is "
"removed from push reminders."
msgstr ""

#. module: website_event_track_social
#: model:ir.model,name:website_event_track_social.model_website_visitor
msgid "Website Visitor"
msgstr "بازدید کننده وبسایت"

#. module: website_event_track_social
#. odoo-python
#: code:addons/website_event_track_social/models/event_track.py:0
msgid "Your favorite track '%(track)s' will start in %(delay)s minutes!"
msgstr ""

#. module: website_event_track_social
#. odoo-python
#: code:addons/website_event_track_social/models/event_track.py:0
msgid "Your track is about to start!"
msgstr ""
