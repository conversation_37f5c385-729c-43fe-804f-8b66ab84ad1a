# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_category__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""
"Permette di definire quali documenti si desidera creare una volta che la "
"richiesta è stata approvata"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_category
msgid "Approval Category"
msgstr "Categoria approvazione"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_request
msgid "Approval Request"
msgstr "Richiesta approvazione"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_category__approval_type
msgid "Approval Type"
msgstr "Tipo approvazione"

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: approvals_purchase
#: model:approval.category,name:approvals_purchase.approval_category_data_rfq
#: model:ir.model.fields.selection,name:approvals_purchase.selection__approval_category__approval_type__purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
msgid "Create RFQ's"
msgstr "Crea RdP"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception occurred: the Approval Request"
msgstr "Si è verificata un'eccezione: la richiesta di approvazione"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception(s):"
msgstr "Eccezioni:"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_product_line.py:0
msgid "Please select a vendor on product line or set it on product(s) %s."
msgstr ""
"Seleziona un fornitore sulla riga prodotto o configuralo sul/i prodotto/i%s."

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_product_line
msgid "Product Line"
msgstr "Riga prodotto"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__product_template_id
msgid "Product Template"
msgstr "Modello prodotto"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__product_id
msgid "Products"
msgstr "Prodotti"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_request__purchase_order_count
msgid "Purchase Order Count"
msgstr "Numero ordini di acquisto"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__purchase_order_line_id
msgid "Purchase Order Line"
msgstr "Riga ordine di acquisto"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
msgid "Purchase Orders"
msgstr "Ordini di acquisto"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__po_uom_qty
msgid "Purchase UoM Quantity"
msgstr "Quantità UdM acquisti"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_res_partner__category_id
#: model:ir.model.fields,field_description:approvals_purchase.field_res_users__category_id
msgid "Tags"
msgstr "Etichette"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_product_line__po_uom_qty
msgid ""
"The quantity converted into the UoM used by the product in Purchase Order."
msgstr ""
"Quantità convertita nella UdM utilizzata dal prodotto nell'ordine di "
"acquisto."

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__seller_id
msgid "Vendors"
msgstr "Fornitori"

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
msgid "You cannot create an empty purchase request."
msgstr "Impossibile creare una richiesta di acquisto vuota."

#. module: approvals_purchase
#. odoo-python
#: code:addons/approvals_purchase/models/approval_request.py:0
msgid "You must select a product for each line of requested products."
msgstr "Devi selezionare un prodotto per ogni linea di prodotti richiesti."

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "cancelled"
msgstr "annullato"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid ""
"has been canceled.\n"
"            Manual actions may be needed."
msgstr ""
"è stato cancellato.\n"
"            Potrebbero essere necessarie azioni manuali."

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "of"
msgstr "di"
