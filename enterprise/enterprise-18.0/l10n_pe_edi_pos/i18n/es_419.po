# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_pe_edi_pos
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-17 08:39+0000\n"
"PO-Revision-Date: 2024-06-17 08:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pe_edi_pos
#. odoo-javascript
#: code:addons/l10n_pe_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Additional Refund Information"
msgstr "Información adicional sobre devoluciones"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__11
msgid "Adjust in the exportation operation"
msgstr "Ajustes de operaciones de exportación"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__12
msgid "Adjust of IVAP"
msgstr "Ajustes afectos al IVAP"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__08
msgid "Bonus"
msgstr "Bonificación"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__02
msgid "Cancellation by error in the RUC"
msgstr "Anulación por error en el RUC"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__01
msgid "Cancellation of the operation"
msgstr "Anulación de la operación"

#. module: l10n_pe_edi_pos
#. odoo-javascript
#: code:addons/l10n_pe_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__03
msgid "Correction by error in the description"
msgstr "Corrección por error en la descripción"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields,field_description:l10n_pe_edi_pos.field_pos_order__l10n_pe_edi_refund_reason
msgid "Credit Reason"
msgstr "Razón del crédito"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__09
msgid "Decrease in value"
msgstr "Disminución en el valor"

#. module: l10n_pe_edi_pos
#. odoo-javascript
#: code:addons/l10n_pe_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Discard"
msgstr "Descarte"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__05
msgid "Discount per item"
msgstr "Descuento por artículo"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__04
msgid "Global discount"
msgstr "Descuento global"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields,help:l10n_pe_edi_pos.field_pos_order__l10n_pe_edi_refund_reason
msgid ""
"It contains all possible values for the refund reason according to Catalog "
"No. 09"
msgstr ""
"Contiene todos los valores posibles para el motivo de nota de crédito "
"Catálogo No. 09"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__10
msgid "Other concepts"
msgstr "Otros Conceptos "

#. module: l10n_pe_edi_pos
#: model:ir.model,name:l10n_pe_edi_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr "Órdenes del Punto de venta"

#. module: l10n_pe_edi_pos
#: model:ir.model,name:l10n_pe_edi_pos.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesión del punto de venta"

#. module: l10n_pe_edi_pos
#. odoo-javascript
#: code:addons/l10n_pe_edi_pos/static/src/app/add_info_popup/add_info_popup.xml:0
#, python-format
msgid "Refund Reason:"
msgstr "Motivo del reembolso"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__07
msgid "Refund per item"
msgstr "Devolución por ítem"

#. module: l10n_pe_edi_pos
#: model:ir.model.fields.selection,name:l10n_pe_edi_pos.selection__pos_order__l10n_pe_edi_refund_reason__06
msgid "Total refund"
msgstr "Devolución total"

#. module: l10n_pe_edi_pos
#. odoo-python
#: code:addons/l10n_pe_edi_pos/models/pos_order.py:0
#, python-format
msgid ""
"You cannot invoice this refund since the related order is not invoiced "
"yet."
msgstr ""
"No puede facturar este reembolso ya que el pedido relacionado aún no se "
"ha facturado."

#. module: l10n_pe_edi_pos
#. odoo-python
#: code:addons/l10n_pe_edi_pos/models/pos_order.py:0
#, python-format
msgid "You cannot refund several invoices at once."
msgstr "No se pueden reembolsar varias facturas a la vez"
