# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_barcodelookup
# 
# Translators:
# hish, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: hish, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "Create New Product"
msgstr ""

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "Inconsistent Barcode"
msgstr ""

#. module: stock_barcode_barcodelookup
#: model:ir.actions.act_window,name:stock_barcode_barcodelookup.stock_barcodelookup_product_product_action
msgid "New Product"
msgstr "Шинэ Бүтээгдэхүүн"

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "No record found for the specified barcode"
msgstr ""

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "Product created successfully"
msgstr ""

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "RPC Error"
msgstr ""

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid "This product doesn't exists"
msgstr ""

#. module: stock_barcode_barcodelookup
#. odoo-javascript
#: code:addons/stock_barcode_barcodelookup/static/src/models/barcode_model.js:0
msgid ""
"This product doesn't exists either scan a package\n"
"                                available at the picking location or create new product"
msgstr ""
