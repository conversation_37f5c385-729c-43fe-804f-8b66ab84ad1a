# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:42+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr "<i class=\"fa fa-download fa-fw\"/> 下载文件"

#. module: documents_hr
#: model:mail.template,body_html:documents_hr.mail_template_document_folder_link
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your personal documents access link is available for you.<br/><br/>\n"
"            You'll find all your personal HR documents in this folder.<br/><br/>\n"
"            Please keep this link secure.<br/><br/>\n"
"            <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                <a t-attf-href=\"{{ object._get_documents_link_url() }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                    Your Documents\n"
"                </a>\n"
"            </div>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            <t t-esc=\"object.name\"/>您好！您的个人文件访问链接已准备好。<br/><br/>\n"
"            该资料夹会存放你所有与人力资源用途相关的个人文件。<br/><br/>\n"
"            请紧记确保此链接的保安。<br/><br/>\n"
"            <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                <a t-attf-href=\"{{ object._get_documents_link_url() }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                    您的文件\n"
"                </a>\n"
"            </div>\n"
"            祝工作顺利！<br/>\n"
"            人力资源团队\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_absences
msgid "Absences"
msgstr "离开"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr "集中员工的文档（合同，工资单等）"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_certification
msgid "Certifications"
msgstr "认证"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "离职向导"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "单据"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "文档数"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_documents_redirect
msgid "Document Redirect"
msgstr "文件重定向"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "文档"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "文档人力资源设置"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "Download file"
msgstr "下载文件"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
#: model:ir.model.fields,field_description:documents_hr.field_documents_redirect__employee_id
msgid "Employee"
msgstr "员工"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid ""
"Employee's related user and private email must be set to use \"Send Access Link\" function:\n"
"%s"
msgstr ""
"工相关用户和私人电子邮件必须设置为使用 “发送访问链接” 功能。\n"
"%s"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_employees
msgid "Employees Documents"
msgstr "员工文档"

#. module: documents_hr
#: model:mail.template,name:documents_hr.mail_template_document_folder_link
msgid "HR: Document Access Link"
msgstr "HR：文档访问链接"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "人力资源"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "My documents"
msgstr "我的文档"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_departure_wizard__private_email
msgid "Private Email"
msgstr "私人电子邮箱"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_departure_wizard__send_hr_documents_access_link
msgid "Send Access Link"
msgstr "发送访问链接"

#. module: documents_hr
#: model:ir.actions.server,name:documents_hr.ir_actions_server_send_access_link
msgid "Send HR Documents Access Link"
msgstr "发送 HR 文档访问链接"

#. module: documents_hr
#: model:ir.model.fields,help:documents_hr.field_hr_departure_wizard__send_hr_documents_access_link
msgid ""
"Send a share link to the private email of the employee with all the HR files"
" he owns in Documents app"
msgstr "向员工个人电子邮箱发送共享链接，包含其在Documents应用程序中拥有的所有人力资源文件"

#. module: documents_hr
#: model:mail.template,description:documents_hr.mail_template_document_folder_link
msgid "Sent manually when recording an employee departure"
msgstr "记录员工离职时手动发送"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "Size:"
msgstr "尺寸："

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid "The access link has been sent to the employee."
msgstr "访问链接已发送给员工。"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "User"
msgstr "用户"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "工作区"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid "You can not send the documents link to the employee."
msgstr "您不能将文件链接发送给员工。"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid ""
"You must set a work contact address on the Employee in order to use "
"Document's features."
msgstr "您必须为员工设置工作联系地址，才能使用文档功能。"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "人力资源工作区"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "人力资源默认工作区"

#. module: documents_hr
#: model:mail.template,subject:documents_hr.mail_template_document_folder_link
msgid "{{ object.name }}, your personal documents access link is available"
msgstr "{{ object.name }}, 您的个人文档访问链接可用"
