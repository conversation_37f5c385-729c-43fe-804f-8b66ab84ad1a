# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_cohort
# 
# Translators:
# <PERSON> <k<PERSON><PERSON><PERSON>.<EMAIL>>, 2024
# 0ba0ac30481a756f36528ba6f9a4317e_6443a87 <52eefe24349934c364624ef40611b7a3_1010754>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2025\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
msgid "%(date_stop)s - By %(interval)s"
msgstr "%(date_stop)s - Sa %(interval)s"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "- By"
msgstr " - Sa"

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Radni prozor"

#. module: web_cohort
#. odoo-javascript
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Average"
msgstr "Prosjek"

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_base
msgid "Base"
msgstr "Osnovica"

#. module: web_cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_actions_act_window_view__view_mode__cohort
#: model:ir.model.fields.selection,name:web_cohort.selection__ir_ui_view__type__cohort
msgid "Cohort"
msgstr ""

#. module: web_cohort
#. odoo-python
#: code:addons/web_cohort/controllers/main.py:0
msgid "Cohort %(title)s (%(model_name)s)"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid "Cohort view has not defined \"date_start\" attribute."
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid "Cohort view has not defined \"date_stop\" attribute."
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Day"
msgstr "Dan"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Download as Excel file"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "Main actions"
msgstr "Glavne radnje"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Month"
msgstr "Mjesec"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.xml:0
msgid "No data available."
msgstr "Nema dostupnih podataka"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_renderer.js:0
msgid ""
"Period: %(period)s\n"
"%(measure)s: %(count)s"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid ""
"The argument %(interval)s is not a valid interval. Here are the intervals: "
"%(intervals)s"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid ""
"The argument %(mode)s is not a valid mode. Here are the modes: %(modes)s"
msgstr ""

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_arch_parser.js:0
msgid ""
"The argument %(timeline)s is not a valid timeline. Here are the timelines: "
"%(timelines)s"
msgstr ""

#. module: web_cohort
#: model:ir.model,name:web_cohort.model_ir_ui_view
msgid "View"
msgstr "Pogledaj"

#. module: web_cohort
#: model:ir.model.fields,field_description:web_cohort.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_cohort.field_ir_ui_view__type
msgid "View Type"
msgstr "Tip pogleda"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Week"
msgstr "Tjedan"

#. module: web_cohort
#. odoo-javascript
#: code:addons/web_cohort/static/src/cohort_model.js:0
msgid "Year"
msgstr "Godina"
