# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_sign
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_count
msgid "# of Signature Requests"
msgstr "# a Cererilor de Semnătură"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: sale_renting_sign
#: model:sign.template,redirect_url_text:sale_renting_sign.template_rental_sign
msgid "Close"
msgstr "Închide"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Default Document"
msgstr "Document Implicit"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid "Default Document Template for Rentals"
msgstr "Model Document pentru Închirieri"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__template_id
msgid "Document Template"
msgstr "Document șablon"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
msgid "Document(s) Signed"
msgstr "Document(e) Semnate"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sale_order
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__order_id
#: model:ir.model.fields,field_description:sale_renting_sign.field_sign_send_request__sale_order_id
msgid "Sales Order"
msgstr "Comandă de vânzare"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Set a default document template for all rentals in the current company"
msgstr ""
"Setați un model de document implicit pentru toate închirierile din compania "
"curentă"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Document"
msgstr "Semnați Documentul"

#. module: sale_renting_sign
#: model:ir.actions.act_window,name:sale_renting_sign.rental_sign_documents
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Documents"
msgstr "Semnare Documente"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_rental_sign_wizard
msgid "Sign Documents from a SO"
msgstr "Semnează documente dintr-un SO"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_send_request
msgid "Sign send request"
msgstr "Semnează solicitarea de trimitere"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_request
msgid "Signature Request"
msgstr "Solicitare Semnătură"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_ids
msgid "Signature Requests"
msgstr "Solicitări Semnătură"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid ""
"This document template will be selected by default when signing documents "
"from a rental order. You should select a template accessible to all Sign "
"users."
msgstr ""
"Acest model de document va fi selectat implicit la semnarea documentelor "
"dintr-o comandă de închiriere. Ar trebui să selectați un model accesibil "
"tuturor utilizatorilor Semn"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.res_config_settings_view_form
msgid "Upload Template"
msgstr "Încărcare șablon"
