# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_sign
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_count
msgid "# of Signature Requests"
msgstr "# próśb o podpis"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: sale_renting_sign
#: model:sign.template,redirect_url_text:sale_renting_sign.template_rental_sign
msgid "Close"
msgstr "Zamknij"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Default Document"
msgstr "Domyślny dokument"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid "Default Document Template for Rentals"
msgstr "Domyślny szablon dokumentu wynajmu"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__template_id
msgid "Document Template"
msgstr "Szablon dokumentu"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
msgid "Document(s) Signed"
msgstr "Podpisany(e) dokument(y)"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sale_order
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__order_id
#: model:ir.model.fields,field_description:sale_renting_sign.field_sign_send_request__sale_order_id
msgid "Sales Order"
msgstr "Zamówienie sprzedaży"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Set a default document template for all rentals in the current company"
msgstr ""
"Ustaw domyślny szablon dokumentu dla wszystkich wynajmów dla aktywnej firmy"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Document"
msgstr "Podpisz dokument"

#. module: sale_renting_sign
#: model:ir.actions.act_window,name:sale_renting_sign.rental_sign_documents
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Documents"
msgstr "Podpisz dokumenty"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_rental_sign_wizard
msgid "Sign Documents from a SO"
msgstr "Podpisz dokumenty z SO"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_send_request
msgid "Sign send request"
msgstr "Podpisz i wyślij prośbę"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_request
msgid "Signature Request"
msgstr "Prośba o podpis"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_ids
msgid "Signature Requests"
msgstr "Prośby o podpis"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid ""
"This document template will be selected by default when signing documents "
"from a rental order. You should select a template accessible to all Sign "
"users."
msgstr ""
"Ten szablon dokumentu zostanie domyślnie wybrany podczas podpisywania "
"dokumentów z zamówienia wynajmu. Powinieneś wybrać szablon dostępny dla "
"wszystkich użytkowników."

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.res_config_settings_view_form
msgid "Upload Template"
msgstr "Załaduj szablon"
