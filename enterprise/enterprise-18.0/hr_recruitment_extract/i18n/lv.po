# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_extract
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Will Sensors, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_applicant
msgid "Applicant"
msgstr "Kandidāts"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_candidate
msgid "Candidate"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Choose a Job Board"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_actions_server_digitize_cv
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "Digitize document"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_error_message
msgid "Error message"
msgstr "Kļūdas ziņojums"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_state_processed
msgid "Extract State Processed"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_state
msgid "Extract state"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_status
msgid "Extract status"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (Partneri)"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__has_message
msgid "Has Message"
msgstr "Ir ziņojums"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_error
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, daži ziņojumi satur piegādes kļūdu."

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__is_in_extractable_state
msgid "Is In Extractable State"
msgstr ""

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Job Board"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.act_window,name:hr_recruitment_extract.ir_module_module_action_open_job_board_modules
msgid "Job Boards"
msgstr "Darba dēļi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Galvenais pielikums"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Ziņojumu, kuriem nepieciešama darbība, skaits"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu, kas satur piegādes kļūdu, skaits"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Post jobs on external job boards"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__rating_ids
msgid "Ratings"
msgstr "Reitingi"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Recruitment OCR: Update All Status"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Recruitment OCR: Validate CV"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_config_settings__recruitment_extract_show_ocr_option_selection
msgid "Recruitment processing option"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Īsziņas piegādes kļūda"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_company__recruitment_extract_show_ocr_option_selection
msgid "Send mode on candidate attachments"
msgstr ""

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__website_message_ids
msgid "Website Messages"
msgstr "Tīmekļa lapas ziņojumi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__website_message_ids
msgid "Website communication history"
msgstr "Tīmekļa lapas komunikācijas vēsture"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_candidate.py:0
msgid "You cannot send a CV for this candidate!"
msgstr ""
