# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_extract
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_applicant
msgid "Applicant"
msgstr "Candidato"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Può mostrare il tasto invio OCR"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_hr_candidate
msgid "Candidate"
msgstr "Candidato"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Choose a Job Board"
msgstr "Scegli una bacheca"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: hr_recruitment_extract
#: model:ir.model,name:hr_recruitment_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Digitalizzare in modo automatico"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_actions_server_digitize_cv
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.hr_recruitment_extract_view_form
msgid "Digitize document"
msgstr "Digitalizza documento"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Digitalizzare solo su richiesta"

#. module: hr_recruitment_extract
#: model:ir.model.fields.selection,name:hr_recruitment_extract.selection__res_company__recruitment_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Non digitalizzare"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_error_message
msgid "Error message"
msgstr "Messaggio di errore"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_state_processed
msgid "Extract State Processed"
msgstr "Estrai elementi elaborati"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_state
msgid "Extract state"
msgstr "Stato estrazione"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_status
msgid "Extract status"
msgstr "Stato estrazione"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID della richiesta per IAP-OCR"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_error
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Può essere estratto"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Job Board"
msgstr "Bacheca annunci di lavoro"

#. module: hr_recruitment_extract
#: model:ir.actions.act_window,name:hr_recruitment_extract.ir_module_module_action_open_job_board_modules
msgid "Job Boards"
msgstr "Bacheche annunci lavoro"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: hr_recruitment_extract
#: model_terms:ir.ui.view,arch_db:hr_recruitment_extract.res_config_settings_view_form
msgid "Post jobs on external job boards"
msgstr "Pubblica offerte su bacheche esterne"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__rating_ids
msgid "Ratings"
msgstr "Valutazioni"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Recruitment OCR: Update All Status"
msgstr "OCR selezione: aggiorna tutti gli stati"

#. module: hr_recruitment_extract
#: model:ir.actions.server,name:hr_recruitment_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Recruitment OCR: Validate CV"
msgstr "OCR selezione: convalida CV"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_config_settings__recruitment_extract_show_ocr_option_selection
msgid "Recruitment processing option"
msgstr "Opzione processo di selezione"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_res_company__recruitment_extract_show_ocr_option_selection
msgid "Send mode on candidate attachments"
msgstr "Modalità di invio per allegati candidato"

#. module: hr_recruitment_extract
#: model:ir.model.fields,field_description:hr_recruitment_extract.field_hr_candidate__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: hr_recruitment_extract
#: model:ir.model.fields,help:hr_recruitment_extract.field_hr_candidate__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: hr_recruitment_extract
#. odoo-python
#: code:addons/hr_recruitment_extract/models/hr_candidate.py:0
msgid "You cannot send a CV for this candidate!"
msgstr "Impossibile inviare un CV per questo candidato!"
