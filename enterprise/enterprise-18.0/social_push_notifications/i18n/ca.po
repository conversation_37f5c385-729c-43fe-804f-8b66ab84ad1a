# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_push_notifications
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON>@hotmail.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Jonatan <PERSON>, 2024
# <PERSON>, 2024
# ma<PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "<span class=\"ps-2\">seconds</span>"
msgstr "<span class=\"ps-2\">segons</span>"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_post_ids
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_post_ids
msgid "All related social media posts"
msgstr "Totes les publicacions relacionades amb les xarxes socials"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
msgid "Allow"
msgstr "Permet"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Body"
msgstr "Cos del missatge"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Delay"
msgstr "Retard"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
msgid "Deny"
msgstr "Denegar"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__display_push_notifications_preview
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__display_push_notifications_preview
msgid "Display Push Notifications Preview"
msgstr "Visualitzar la vista prèvia de les notificacions push"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_post__visitor_domain
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__visitor_domain
msgid "Domain to send push notifications to visitors."
msgstr "Domini per enviar notificacions d'enviament als visitants."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_enable_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_enable_push_notifications
msgid "Enable Web Push Notifications"
msgstr "Habilitar les notificacions Web Push"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Enable push notifications to be notified about new features."
msgstr ""
"Habilitar que les notificacions d'enviament es notifiquin sobre noves "
"característiques."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_admin_key_file
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_admin_key_file
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_admin_key_file
msgid "Firebase Admin Key File"
msgstr "Arxiu de claus d'administració de Firebase"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
msgid "Firebase Admin Key File is missing from the configuration."
msgstr ""
"Falta l'arxiu de claus d'administració de Firebase en la configuració."

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_project_id
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_project_id
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_project_id
msgid "Firebase Project ID"
msgstr "ID del projecte Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_push_certificate_key
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_push_certificate_key
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_push_certificate_key
msgid "Firebase Push Certificate Key"
msgstr "Tecla de certificat Push de la base de foc"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_sender_id
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_sender_id
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_sender_id
msgid "Firebase Sender ID"
msgstr "ID remitent de Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_web_api_key
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_web_api_key
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_web_api_key
msgid "Firebase Web API Key"
msgstr "Clau de l'API web de Firebase"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.push_notifications_preview
msgid "Google Chrome ·"
msgstr "Google Chrome ·"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__has_push_notifications_account
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__has_push_notifications_account
msgid "Has Push Notifications Account"
msgstr ""

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__id
msgid "ID"
msgstr "ID"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Icon"
msgstr "Icona"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Icon Image"
msgstr "Imatge de la icona"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Key File"
msgstr "Arxiu de claus"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_view_form
msgid "Local Time"
msgstr "Hora local"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_media__media_type
msgid "Media Type"
msgstr "Tipus de contingut multimèdia"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Message"
msgstr "Missatge"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_live_post.py:0
#: code:addons/social_push_notifications/models/social_post_template.py:0
msgid "New Message"
msgstr "Nou missatge"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_delay
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_delay
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_delay
msgid "Notification Request Delay (seconds)"
msgstr "Retard de la sol·licitud de notificació (segons)"

#. module: social_push_notifications
#. odoo-javascript
#: code:addons/social_push_notifications/static/src/xml/social_push_notifications_templates.xml:0
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_icon
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_icon
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_icon
msgid "Notification Request Icon"
msgstr "Icona de sol·licitud de notificació"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_body
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_body
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_body
msgid "Notification Request Text"
msgstr "Text de sol·licitud de notificació"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__notification_request_title
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__notification_request_title
#: model:ir.model.fields,field_description:social_push_notifications.field_website__notification_request_title
msgid "Notification Request Title"
msgstr "Títol de la sol·licitud de notificació"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Notification Title"
msgstr "Títol de la notificació"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_kanban
msgid "Notifications"
msgstr "Notificacions"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_push_notifications_count
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_push_notifications_count
msgid "Number Of Push Notifications"
msgstr "Nombre de notificacions per enviament"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_post_template.py:0
msgid "Please specify a Notification Message."
msgstr ""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Project ID"
msgstr "Identificació del projecte"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_kanban
msgid "Push"
msgstr "Empeny"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Push Certificate Key ID"
msgstr "Identificador de clau de certificat Push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_live_post__push_notification_image
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_image
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_image
msgid "Push Icon Image"
msgstr "Empeny la imatge de la icona"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Push Notification"
msgstr ""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_search
msgid "Push Notification Off"
msgstr "Notificació push desactivada"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_search
msgid "Push Notification On"
msgstr "Notificació push activada"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Push Notification Options"
msgstr "Opcions de notificacions push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_live_post__push_notification_title
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_title
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_title
msgid "Push Notification Title"
msgstr "Títol de la notificació push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_marketing_campaign__social_push_notification_ids
#: model:ir.model.fields,field_description:social_push_notifications.field_utm_campaign__social_push_notification_ids
#: model:ir.model.fields.selection,name:social_push_notifications.selection__social_media__media_type__push_notifications
#: model:social.media,name:social_push_notifications.social_media_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
msgid "Push Notifications"
msgstr "Notificacions Push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor__has_push_notifications
msgid "Push Notifications Enabled"
msgstr "Notificacions de pujada activades"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_message
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_message
msgid "Push Notifications Message"
msgstr ""

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notifications_preview
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notifications_preview
msgid "Push Notifications Preview"
msgstr "Vista previa de las notificaciones push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__push_token
msgid "Push Subscription"
msgstr "Subscripció Push"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website_visitor_push_subscription
msgid "Push Subscription for a Website Visitor"
msgstr "Push Subscription for a Website Visitor"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor__push_subscription_ids
msgid "Push Subscriptions"
msgstr "Push Subscriptions"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_live_post__push_notification_target_url
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__push_notification_target_url
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__push_notification_target_url
msgid "Push Target URL"
msgstr "URL de la destinació de Push"

#. module: social_push_notifications
#: model:ir.model.constraint,message:social_push_notifications.constraint_website_visitor_push_subscription_push_token_uniq
msgid "Push token can't be duplicated!"
msgstr "No es pot duplicar el token d'empenta!"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_live_post__reached_visitor_ids
msgid "Reached Visitors"
msgstr "Visitants aconseguits"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.utm_campaign_view_form
msgid "Send Push"
msgstr "enviar embranzida"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:social_push_notifications.website_visitor_view_tree
msgid "Send Push Notification"
msgstr "Enviar notificació Push"

#. module: social_push_notifications
#: model:ir.actions.server,name:social_push_notifications.social_send_push_notifications_action_server
msgid "Send Push Notifications"
msgstr "Enviar notificacions Push"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__use_visitor_timezone
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__use_visitor_timezone
msgid "Send at Visitors' Timezone"
msgstr "Envia a la zona horària dels visitants"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid ""
"Send push notifications and configure this website's notifications "
"permission request"
msgstr ""
"Envia notificacions d'enviament i configura la sol·licitud de permís de "
"notificació d'aquest lloc web"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Sender ID"
msgstr "Identificació del remitent"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_account
msgid "Social Account"
msgstr "Compte social"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_live_post
msgid "Social Live Post"
msgstr "Publicació social en directe"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_media
msgid "Social Media"
msgstr "Xarxes socials"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_post
msgid "Social Post"
msgstr "Publicació social"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicació social"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/website_visitor.py:0
msgid "Some selected visitors do not allow push notifications."
msgstr "Alguns visitants seleccionats no permeten notificacions d'enviament."

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.social_post_template_view_form
msgid "Target URL"
msgstr "URL de destí"

#. module: social_push_notifications
#: model:ir.model.constraint,message:social_push_notifications.constraint_social_account_website_unique
msgid "There is already a configuration for this website."
msgstr "Ja hi ha una configuració per a aquest lloc web."

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_account__website_id
msgid ""
"This firebase configuration will only be used for the specified website"
msgstr ""
"Aquesta configuració de firebase només s'utilitzarà per al lloc web "
"especificat"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_live_post__push_notification_image
#: model:ir.model.fields,help:social_push_notifications.field_social_post__push_notification_image
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__push_notification_image
msgid "This icon will be displayed in the browser notification"
msgstr "Aquesta icona es mostrarà a la notificació del navegador"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Title"
msgstr "Títol"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_utm_campaign
msgid "UTM Campaign"
msgstr "Campanya UTM"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Use your own Firebase Account for this website's push notifications"
msgstr ""
"Utilitzeu el vostre propi compte de Firebase per a les notificacions "
"d'enviament d'aquest lloc web"

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_res_config_settings__firebase_use_own_account
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__firebase_use_own_account
#: model:ir.model.fields,field_description:social_push_notifications.field_website__firebase_use_own_account
msgid "Use your own Firebase account"
msgstr "Utilitza el teu propi compte de Firebase"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social_push_notifications
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post__visitor_domain
#: model:ir.model.fields,field_description:social_push_notifications.field_social_post_template__visitor_domain
msgid "Visitor Domain"
msgstr "Domini del visitant"

#. module: social_push_notifications
#: model:ir.ui.menu,name:social_push_notifications.social_visitor
msgid "Visitors"
msgstr "Visitants"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Want to discover new versions?"
msgstr "¿Vols descobrir noves versions?"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "Web API Key"
msgstr "Clau de l'API web"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website
#: model:ir.model.fields,field_description:social_push_notifications.field_social_account__website_id
msgid "Website"
msgstr "Lloc web"

#. module: social_push_notifications
#: model:ir.model,name:social_push_notifications.model_website_visitor
#: model:ir.model.fields,field_description:social_push_notifications.field_website_visitor_push_subscription__website_visitor_id
msgid "Website Visitor"
msgstr "Visitant del lloc web"

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
msgid "You can't delete a Push Notification account."
msgstr "No pots eliminar un compte de notificació de Push."

#. module: social_push_notifications
#. odoo-python
#: code:addons/social_push_notifications/models/social_account.py:0
msgid ""
"You have to install \"google_auth>=1.18.0\" to be able to send push "
"notifications."
msgstr ""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"************\""
msgstr "p. ex. «************»"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"BIzbSyXhhsFHEgphW55CSg5cV7h7c_S-AuTMKc9\""
msgstr "p. ex. «BIzbSyXhhsFHEgphW55CSg5cV7h7c\"S-AuTMKc9»"

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"CCSc77KP_LX8dTAogFakOoJ_VqNP15u0-43psDJe__a9B...\""
msgstr "p. ex.. \"CCSc77KP_LX8dTAogFakOoJ_VqNP15u0-43psDJe__a9B...\""

#. module: social_push_notifications
#: model_terms:ir.ui.view,arch_db:social_push_notifications.res_config_settings_view_form
msgid "e.g. \"my-project-id\""
msgstr "p. ex. «my-project-id»"

#. module: social_push_notifications
#: model:ir.model.fields,help:social_push_notifications.field_social_post__use_visitor_timezone
#: model:ir.model.fields,help:social_push_notifications.field_social_post_template__use_visitor_timezone
msgid ""
"e.g: If you post at 15:00 your time, all visitors will receive the post at "
"15:00 their time."
msgstr ""
"Per exemple: Si publiqueu a les 15:00 del vostre temps, tots els visitants "
"rebran el missatge a les 15:00 del seu temps."
