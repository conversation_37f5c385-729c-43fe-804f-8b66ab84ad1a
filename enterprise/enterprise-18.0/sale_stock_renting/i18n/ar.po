# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock_renting
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s item)"
msgstr "%(product)s (%(qty)s عنصر) "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s items)"
msgstr "%(product)s (%(qty)s عناصر) "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Some products don't have the requested qty available for pickup\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            بعض المنتجات ليس لها الكمية المطلوبة متاحة لاستلامها\n"
"                        </span>"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Activate to use stock deliveries and receipts for rental orders"
msgstr "قم بالتفعيل لاستخدام عمليات توصيل المخزون والإيصالات لأوامر التأجير "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered unavailable "
"prior to renting (preparation time)."
msgstr ""
"الوقت (بالساعات) الذي يتم اعتبار المنتج فيه غير متاح قبل التأجير (وقت "
"التجهيز). "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_config_settings__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered "
"unavailableprior to renting (preparation time)."
msgstr ""

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "التوافر"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__qty_available
msgid "Available"
msgstr "متاح"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__available_reserved_lots
msgid "Available Reserved Lots"
msgstr "المجموعات المحجوزة المتاحة "

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available for Rent"
msgstr "متاح للتأجير "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Default Padding Time"
msgstr "وقت الفحص والترميم الافتراضي "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_sale_order_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "تأكد من إمكانية تتبع المنتج القابل للتخزين في مستودعك. "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_lines_missing_stock
msgid "Has lines whose products have insufficient stock"
msgstr "يحتوي على بنود المنتجات التي ليس لها مخزون كافٍ "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_tracked_lines
msgid "Has lines with tracked products"
msgstr "يحتوي على بنود مع منتجات متتبعة "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__is_available
msgid "Is Available"
msgstr "متاح "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__is_product_storable
msgid "Is Product Storable"
msgstr "المنتج قابل للتأجير "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_lot
msgid "Lot/Serial"
msgstr "رقم الدفعة/الرقم التسلسلي "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "قاعدة إعادة الطلب"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Minimum amount of time between two rentals"
msgstr "الحد الأدنى للوقت بين عمليتي تأجير "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid ""
"No valid quant has been found in location %(location)s for serial number "
"%(serial_number)s!"
msgstr ""
"لم يتم العثور على كمية صالحة في الموقع %(location)s للرقم التسلسلي "
"%(serial_number)s! "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__padding_time
msgid "Padding"
msgstr "وقت الفحص والترميم "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__padding_time
msgid "Padding Time"
msgstr "وقت الفحص والترميم "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "استلام/إرجاع المنتجات "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickeable_lot_ids
msgid "Pickeable Lot"
msgstr "دفعة يمكن استلامها "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickedup_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__pickedup_lot_ids
msgid "Pickedup Lot"
msgstr "استلام الدفعة "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers picked up for the tracked products."
msgstr "نيرجى تحديد الأرقام التسلسلية المختارة للمنتجات المتتبعة. "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers returned for the tracked products."
msgstr "يرجى تحديد الأرقام التسلسلية المرجعة للمنتجات المتتبعة. "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_template
msgid "Product"
msgstr "المنتج"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: sale_stock_renting
#: model:stock.route,name:sale_stock_renting.route_rental
msgid "Rental"
msgstr "تأجير "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "تقرير تحليل التأجير "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__rental_loc_id
msgid "Rental Location"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "جدول التأجير "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Rental Transfers"
msgstr "تحويلات التأجير "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "Rental move: %(order)s"
msgstr "حركة التأجير: %(order)s "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__group_rental_stock_picking
msgid "Rental pickings"
msgstr "عمليات انتقاء التأجير "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "تمثيل لبند أمر التأجير العابر "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__reserved_lot_ids
msgid "Reserved Lot"
msgstr "الدفعة المحجوزة "

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved lots unavailable"
msgstr "المجموعات المحجوزة غير متاحة "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_return_picking
msgid "Return Picking"
msgstr "إرجاع الشحنة المنتقاة "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returnable_lot_ids
msgid "Returnable Lot"
msgstr "الدفعة التي يمكن إرجاعها "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returned_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__returned_lot_ids
msgid "Returned Lot"
msgstr "الدفعة التي تم إرجاعها "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_template__preparation_time
msgid "Security Time"
msgstr "وقت الأمان "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_report__lot_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__lot_id
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_report_search_view_inherit_lots
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_search_inherit_lots
msgid "Serial Number"
msgstr "الرقم التسلسلي "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.report_rental_order_document
msgid "Serial Numbers"
msgstr "الأرقام التسلسلية "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_move
msgid "Stock Move"
msgstr "حركة المخزون"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,help:sale_stock_renting.field_product_template__preparation_time
msgid "Temporarily make this product unavailable before pickup."
msgstr "جعل هذا المنتج غير متاح مؤقتاً قبل الاستلام. "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_gantt_inherit_stock
msgid "The product is not available during this period."
msgstr "المنتج غير متاح خلال هذه الفترة. "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__rental_loc_id
msgid ""
"This technical location serves as stock for products currently in rentalThis"
" location is internal because products in rentalare still considered as "
"company assets."
msgstr ""
"يعمل هذا الموقع التقني كمخزون للمنتجات قيد التأجير حالياً. هذا الموقع داخلي "
"لأن المنتجات قيد التأجير لا تزال تعد من أصول الشركة. "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__tracking
msgid "Tracking"
msgstr "التتبع"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_template.py:0
msgid ""
"Tracking by lots isn't supported for rental products.\n"
"You should rather change the tracking mode to unique serial numbers."
msgstr ""
"عملية التتبع حسب المجموعة غير مدعومة لمنتجات التأجير. \n"
"عليك تغيير نوع التتبع إلى الأرقام التسلسلية الفريدة. "

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__unavailable_lot_ids
msgid "Unavailable Lot"
msgstr "الدفعة غير متاحة "

#. module: sale_stock_renting
#: model:res.groups,name:sale_stock_renting.group_rental_stock_picking
msgid "Use pickings for rental orders"
msgstr "استخدم عمليات الانتقاء لأوامر التأجير "

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Rentals"
msgstr "عرض التأجيرات "

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__warehouse_id
msgid "Warehouse"
msgstr "المستودع "

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "You cannot change the product of lines linked to stock moves."
msgstr "لا يمكنك تغيير منتج لبنود مرتبطة بحركة مخزون. "

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "hours"
msgstr "ساعات"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.product_template_inherit_view_form_stock_rental
msgid "hours before orders"
msgstr "الساعات قبل الطلبات "

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "to"
msgstr "إلى"
