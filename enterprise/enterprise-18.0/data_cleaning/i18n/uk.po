# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_cleaning
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_group.py:0
msgid "%(model)s - Similarity: %(similarity)s%%"
msgstr "%(model)s - Схожість: %(similarity)s%%"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "%s (copy)"
msgstr "%s (копія)"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "%s records have been merged"
msgstr "%s записи об'єднано"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' правило вилучення дублікатів.<br/>\n"
"Ви можете об'єднати їх"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid ""
"' field cleaning rule.<br/>\n"
"You can validate changes"
msgstr ""
"' правило очищення поля.<br/>\n"
"Ви можете підтвердити зміни"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">Кожен</span>"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span invisible=\"not custom_merge_method\">Model specific</span>"
msgstr "<span invisible=\"not custom_merge_method\">Специфічна модель</span>"

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "Поле може з'являтися лише раз!"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Action"
msgstr "Дія"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_display
msgid "Action Display"
msgstr "Відображення дії"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_technical
msgid "Action Technical"
msgstr "Технічна дія"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__action
msgid "Actions"
msgstr "Дії"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__active
msgid "Active"
msgstr "Активно"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__lower
msgid "All Lowercase"
msgstr "Усі скорочення"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__all
msgid "All Spaces"
msgstr "Усі пробіли"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__upper
msgid "All Uppercase"
msgstr "Усі з великої літери"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "Архів"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Archived"
msgstr "Заархівовано"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"Ви впевнені, що хочете об'єднати обрані записи у їх відповідній групі?"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "Are you sure that you want to merge these records?"
msgstr "Ви впевнені, що хочете об'єднати ці записи?"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__automatic
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "Автоматичний"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_search
msgid "Can Be Merged"
msgstr "Можна об'єднати"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_case
msgid "Case"
msgstr "Випадок"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_rule.py:0
msgid "Case/Accent Insensitive Match"
msgstr "Співставити регістр/невиразне виділення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__cleaning_mode
msgid "Cleaning Mode"
msgstr "Режим очищення"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__cleaning_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__cleaning_model_id
msgid "Cleaning Model"
msgstr "Модель очищення"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_record
msgid "Cleaning Record"
msgstr "Запис очищення"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_rule
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Cleaning Rule"
msgstr "Правило очищення"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Cleaning Rules"
msgstr "Правило очищення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__company_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__company_id
msgid "Company"
msgstr "Компанія"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "Налаштуйте правила, щоби визначити записи дублікатів"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "Configure rules to identify records to clean"
msgstr "Налаштуйте правила, щоб визначити записи для очищення"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"Дія контекстного меню, яка переспрямовує до перегляду дедублікатів "
"data_merge."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__country_id
msgid "Country"
msgstr "Країна"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "Створено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "Створено на"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "Створив"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__create_date
msgid "Created on"
msgstr "Створено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "Крос-компанія"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__current_value
msgid "Current"
msgstr "Поточний"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "Метод кастомного злиття"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_clean_records_ir_actions_server
msgid "Data Cleaning: Clean Records"
msgstr "Очищення даних: Очистити записи"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_cleanup_ir_actions_server
msgid "Data Merge: Cleanup Records"
msgstr "Об'єднання даних: Записи очищення"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_find_duplicates_ir_actions_server
msgid "Data Merge: Find Duplicate Records"
msgstr "Об'єднання даних: Знайти записи дублікатів"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_cleaning_model.py:0
msgid "Data to Clean"
msgstr "Дані для очищення"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "Дні"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Deduplicate"
msgstr "Дублікат"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_cleaning.menu_data_merge_group
msgid "Deduplication"
msgstr "Дублювання"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_group
msgid "Deduplication Group"
msgstr "Група дублювання"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "Модель дублювання"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_record
msgid "Deduplication Record"
msgstr "Запис дублювання"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "Правило дублювання"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_config
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "Правила дублювання"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Deduplication is forbidden on the model: %s"
msgstr "У моделі заборонено дедуплікацію: %s"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "Видалити"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_tree
msgid "Details"
msgstr "Деталі"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__differences
msgid "Differences"
msgstr "Різниці"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "Різниця з основним записом"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_form
msgid "Disable Merge"
msgstr "Вимкнути об’єднання"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "Discard"
msgstr "Відмінити"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Discarded"
msgstr "Відмінено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "Дивергентні поля"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__domain
msgid "Domain"
msgstr "Домен"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "Вилучення дублікату"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Duplicates"
msgstr "Дублікат"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Duplicates to Merge"
msgstr "Дублікати для об'єднання"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr "Дублікати із подібністю нижче цього порогу не пропонуватимуться"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.ir_model_view_form
msgid "Enable Merge"
msgstr "Увімкнути об’єднання"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_rule.py:0
msgid "Exact Match"
msgstr "Точне співставлення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__field_id
msgid "Field"
msgstr "Поле"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_record
msgid "Field Cleaning"
msgstr "Поле очищення"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record_notification
msgid "Field Cleaning Records"
msgstr "Записи поля очищення"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_config
msgid "Field Cleaning Rules"
msgstr "Правила поля очищення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__name
msgid "Field Name"
msgstr "Назва поля"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_field_form
msgid "Field To Clean"
msgstr "Поле для очищення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__field_values
msgid "Field Values"
msgstr "Значення поля"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__first
msgid "First Letters to Uppercase"
msgstr "Перші літери з великої букви"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__phone
msgid "Format Phone"
msgstr "Формат телефону"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_search
msgid "Group By"
msgstr "Групувати за"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "Сховати кнопку дії об'єднання"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_case
msgid ""
"How the type case is set by the rule. 'First Letters to Uppercase' sets "
"every letter to lowercase except the first letter of each word, which is set"
" to uppercase."
msgstr ""
"Як тип встановлюється за правилом. \"Перші літери з великої\" встановлює "
"кожну літеру в нижній регістр, за винятком першої літери кожного слова, яка "
"має великі літери."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "I've identified"
msgstr "Я вже ідентифікував"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__id
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__id
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"Якщо значення True, загальний інструмент об’єднання даних доступний у "
"контекстному меню цієї моделі."

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"Якщо True, цей запис використовується для дії контекстного меню «Об’єднати» "
"на цільовій моделі."

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"Якщо в моделі вже є користувацький метод злиття, атрибут класу `_merge_disabled` має значення true в\n"
" цій моделі, і загальна дія злиття даних не повинна бути доступна для цієї моделі."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "Видалено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "Відмінено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__is_master
msgid "Is Master"
msgstr "Основний"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__last_notification
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "Останнє сповіщення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "Список інших моделей з посиланням на цей запис"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "List of users to notify when there are new records to clean"
msgstr ""
"Список користувачів для сповіщення, коли будуть нові записи для очищення"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr ""
"Список користувачів для їх сповіщення, коли будуть нові записи для "
"об'єднання"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__manual
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "Вручну"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.ir_model_action_merge
#: model:ir.ui.menu,name:data_cleaning.ir_model_menu_merge_action_manager
msgid "Manual Merge"
msgstr ""

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Manual Selection - %s"
msgstr "Ручний вибір - %s"

#. module: data_cleaning
#. odoo-javascript
#. odoo-python
#: code:addons/data_cleaning/models/ir_model.py:0
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.xml:0
#: model:ir.actions.server,name:data_cleaning.merge_action_res_country
#: model:ir.actions.server,name:data_cleaning.merge_action_res_country_state
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_category
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_industry
#: model:ir.actions.server,name:data_cleaning.merge_action_res_partner_title
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_list
msgid "Merge"
msgstr "З’єднати"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "Об'єднати, якщо"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "Режим об'єднання"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "Дія об'єднання на сервері"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "Прикріплення дії об'єднання"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_model.py:0
msgid "Missing required PostgreSQL extension: unaccent"
msgstr "Відсутнє обов’язкове розширення PostgreSQL: без акценту"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__res_model
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_storage_search
msgid "Model"
msgstr "Модель"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "Назва моделі"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_ir_model
msgid "Models"
msgstr "Моделі"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "Місяці"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__name
msgid "Name"
msgstr "Назва"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "No cleaning suggestions"
msgstr "Немає пропозицій для очищення"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_merge_record_notification
msgid "No duplicates found"
msgstr "Не знайдено дублікатів"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "Повідомити"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency_period
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Сповіщати періодично"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_user_ids
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "Сповістити користувачів"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Query Failed."
msgstr "Помилка запиту."

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__record_ids
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__res_id
msgid "Record"
msgstr "Запис"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__group_id
msgid "Record Group"
msgstr "Група запису"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__res_id
msgid "Record ID"
msgstr "ID запису"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__name
msgid "Record Name"
msgstr "Назва запису"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Records"
msgstr "Записи"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__records_to_clean_count
msgid "Records To Clean"
msgstr "Записи для очищення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "Підрахунок записів для об'єднання"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "Записи, придатні для процесу дедуплікації"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "Records must be of the same model"
msgstr "Записи мають бути тієї ж моделі"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr ""
"Записи із відсотком схожості вище цього порогу будуть автоматично об’єднані"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__name
msgid "Resource Name"
msgstr "Назва ресурсу"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__rule_ids
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_record_search
msgid "Rule"
msgstr "Правило"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__rule_ids
msgid "Rules"
msgstr "Правила"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__html
msgid "Scrap HTML"
msgstr "Брак HTML"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "Оберіть модель, щоб налаштувати правила вилучення дублікатів"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__sequence
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__case
msgid "Set Type Case"
msgstr "Оберіть тип випадку"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "Схожість %"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "Поріг схожості"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr "Коефіцієнт схожості на основі суми точно схожих текстових полів."

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_storage
#: model:ir.model,name:data_cleaning.model_ir_attachment_report
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_storage
msgid "Storage"
msgstr "Зберігання"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/report/data_storage.py:0
msgid "Storage Detail: %(name)s"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr ""
"Пропонуємо об'єднати співставлені записи принаймні за одним із цих правил"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value_display
msgid "Suggested"
msgstr "Запропонований"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value
msgid "Suggested Value"
msgstr "Запропоноване значення"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "Поріг пропозиції"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__superfluous
msgid "Superfluous Spaces"
msgstr "Зайві пробіли"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
msgid ""
"The Python module `phonenumbers` is not installed. Format phone will not "
"work."
msgstr ""
"Модуль Python `phonenumbers` не встановлено. Формат телефону не буде "
"працювати."

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_cleaning_model_check_notif_freq
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "Період сповіщень повинен бути більше 0."

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "The referenced record does not exist"
msgstr "Запис, на який посилається, не існує"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.js:0
msgid "The selected records have been merged"
msgstr "Вибрані записи об'єднані"

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "The target model does not exists."
msgstr "Цільова модель не існує."

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "There is not referenced record"
msgstr "Немає записів, що посилаються"

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "Цю назву уже взяли"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_ir_attachment_report__size
msgid "Total Size"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_trim
msgid "Trim"
msgstr "Забрати"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__trim
msgid "Trim Spaces"
msgstr "Забрати пробіли"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "Унікальне поле ID"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_cleaning_list_view.xml:0
#: code:addons/data_cleaning/static/src/views/data_merge_list_view.xml:0
msgid "Unselect"
msgstr "Не обрано"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "Оновлено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "Оновлено"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_merge_record__used_in
msgid "Used In"
msgstr "Використано в"

#. module: data_cleaning
#. odoo-javascript
#: code:addons/data_cleaning/static/src/views/data_cleaning_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Validate"
msgstr "Підтвердити"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Тижні"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr "Коли буде включено, дублікати різних компаній будуть запропоновані"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_trim
msgid ""
"Which spaces are trimmed by the rule. Leading, trailing, and successive "
"spaces are considered superfluous."
msgstr ""
"Які пробіли забрані правилом. Перші, останні та послідовні пробіли "
"вважаються зайвими."

#. module: data_cleaning
#. odoo-python
#: code:addons/data_cleaning/models/data_merge_record.py:0
msgid "You must select at least two %s in order to merge them."
msgstr "Ви повинні вибрати принаймні два %s, щоби об'єднати їх ."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "записи дублікатів з '"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_duplicate
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "here"
msgstr "тут"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_merged
msgid "merged into"
msgstr "об'єднано в"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.data_merge_main
msgid "merged into this one"
msgstr "об'єднано у цей"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "records to clean with the '"
msgstr "записи для очищення з '"
