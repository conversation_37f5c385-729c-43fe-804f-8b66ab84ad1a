# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_attendance
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,help:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        يحدد المصدر لإنشاء قيود العمل\n"
"\n"
"        جدول العمل: سيتم إنشاء قيود العمل من ساعات العمل أدناه.\n"
"        الحضور: سيتم إنشاء قيود العمل من سجل حضور الموظف. (يتطلب تطبيق الحضور)\n"
"        التخطيط: سيتم إنشاء قيود العمل من تخطيط الموظف. (يتطلب تطبيق التخطيط)\n"
"    "

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_work_entry__attendance_id
msgid "Attendance"
msgstr "الحاضرين "

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields.selection,name:hr_work_entry_contract_attendance.selection__hr_contract__work_entry_source__attendance
msgid "Attendances"
msgstr "الحضور"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry
msgid "HR Work Entry"
msgstr "قيد عمل الموارد البشرية "

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "إعادة إنشاء قيود عمل الموظفين "

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"delete it."
msgstr "سجل الحضور مرتبط بقيد عمل تم تصديقه. لا يمكنك حذفه. "

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"modify it."
msgstr "سجل الحضور مرتبط بقيد عمل تم تصديقه. لا يمكنك تعديله. "

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "مصدر قيد العمل "
