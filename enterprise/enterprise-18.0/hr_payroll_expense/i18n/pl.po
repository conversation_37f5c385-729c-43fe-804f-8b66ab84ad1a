# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.view_account_payment_register_form_inherit_hr_payroll_expense
msgid ""
"<span class=\"fw-bold\">\n"
"                            The expenses will already be reimbursed in a payslip. Make sure this payment is intended.\n"
"                        </span>"
msgstr ""

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "<span class=\"o_stat_text\">Payslip</span>"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Raport wydatków"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Expense reimbursement rule needs to be configured to add expenses to payslips.\n"
"Please create one salary rule with the \"%(code)s\" code on the relevant salary structures."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") was removed from the next payslip."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") will be added to the next payslip."
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "Wydatki"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr "Liczba wydatków"

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr "Zwrot wydatków"

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr "Wydatki do zwrotu na rzecz pracownika"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Go to salary rules"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_account_payment_register__is_already_paid_through_a_payslip
msgid "Is Already Paid Through A Payslip"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_move
msgid "Journal Entry"
msgstr "Zapis dziennika"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"No debit account found in the '%(rule_name)s' payslip salary rule. Please "
"add a payable debit account to be able to create an accounting entry for the"
" expense reports linked to this payslip."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"No salary rule was found to handle expenses in structure "
"'%(structure_name)s'."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Only approved expense reports that were paid by an employee can be "
"reimbursed in a payslip."
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_payment_register
msgid "Pay"
msgstr "Zapłać"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "Pasek wypłaty"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "Pasek wypłaty"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr "Zwrot w następnym odcinku wypłaty"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid "Reimbursed Expenses"
msgstr "Zwrócone wydatki"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Remove from Payslip"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.actions.server,name:hr_payroll_expense.hr_expense_add_to_payslip_action_server
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr "Raport w następnym odcinku wypłaty"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "Struktura wynagrodzenia"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"The '%(account_name)s' account for the salary rule '%(rule_name)s' must be "
"of type 'Payable'."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"The salary rules with the code 'EXPENSES' must have a debit account set to "
"be able to properly reimburse the linked expenses. This must be an account "
"of type 'Payable'."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"The state of the accounting entries linked to this expense report do not "
"allow it to be reimbursed through a payslip."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "There are no valid expense sheets selected."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot remove an expense from a payslip that has already been validated.\n"
"Expenses can only be removed from draft or canceled payslips."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"You don't have the access rights to link an expense report to a payslip. You"
" need to be a payroll officer to do that."
msgstr ""

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/account_move.py:0
msgid "You don't have the access rights to post an invoice."
msgstr "Nie masz uprawnień dostępu do zaksięgowania faktury."
