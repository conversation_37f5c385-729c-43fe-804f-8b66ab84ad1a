# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "(between 1.91:1 and 4:5)."
msgstr "(entre 1.91:1 y 4:5)."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "HACE 34 SEGUNDOS"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comentarios\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                At least one image is required when posting on Instagram."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                Necesita al menos una imagen para publicar en Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                You can only post up to 10 images at once."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                Solo puede publicar hasta 10 imágenes a la vez."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                Your image appears to be corrupted, please try loading it again."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                Parece que su imagen tiene un error, intente subirla de nuevo."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"Me gusta\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">Mi página de Instagram</span>"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "ID de la aplicación"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "Secreto de la aplicación"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "At least one image is required when posting on Instagram."
msgstr "Necesita al menos una imagen para publicar en Instagram."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "Author Image"
msgstr "Autor de la imagen"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr ""
"Marque esto si quiere usar su propia cuenta de desarrollador de Instagram en"
" lugar de la proporcionada."

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Could not find any account to add."
msgstr "No se encontró una cuenta que agregar."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "Mostrar la vista previa de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"ID de la cuenta de Facebook proporcionada por la API de Facebook, esto jamás se debe configurar manualmente.\n"
"La cuenta (\"profesional\") de Instagram siempre está vinculada a una cuenta de Facebook."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__has_instagram_account
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__has_instagram_account
msgid "Has Instagram Account"
msgstr "Tiene una cuenta de Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Images"
msgstr "Imágenes"

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Instagram"
msgstr "Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "Token de acceso de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"Token de acceso a Instagram que proporcionó la API de Facebook, esto jamás se debe configurar de forma manual.\n"
"            Se usa para autentificar solicitudes cuando se publica o se lee información de esta cuenta."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "ID de la cuenta de Instagram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""
"ID de la cuenta de Instagram que proporcionó la API de Facebook, esto jamás "
"se debe configurar de forma manual."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "ID de la aplicación de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "Secreto de la aplicación de Instagram"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
msgid "Instagram Comments"
msgstr "Comentarios de Instagram"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "Cuenta de desarrollador de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "ID de la cuenta de Instagram y Facebook"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "ID del autor de Instagram y Facebook"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_ids
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_ids
msgid "Instagram Images"
msgstr "Imágenes de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "Me gusta de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_message
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_message
msgid "Instagram Message"
msgstr "Mensaje de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "ID de la publicación de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "URL de la publicación de Instagram"

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "Publicaciones de Instagram"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "Vista previa de Instagram"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Instagram did not provide a valid access token."
msgstr "Instagram no proporcionó un token de acceso válido."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
msgid "Likes"
msgstr "Me gusta"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "Tipo de medios"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Message"
msgstr "Mensaje"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr ""
"Verifique que los comentarios están habilitados para esta publicación en la "
"plataforma."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post_template.py:0
msgid ""
"Please specify either a Instagram Message or upload some Instagram Images."
msgstr ""
"Especifique ya sea un mensaje de Instagram o suba imágenes de Instagram."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "Publicar imagen"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Read More about Instagram Accounts"
msgstr "Conozca más sobre las cuentas de Instagram"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "Cuenta de red social"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "Publicación en redes sociales en vivo"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "Redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "Publicación en redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "Plantilla de publicación en redes sociales"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "Flujo social"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "Publicación en el flujo social"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr ""
"El ID de Facebook del autor de esta publicación de Instagram que se usa para"
" obtener la imagen de peril."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "The following images are not in the correct format (jpg/jpeg)."
msgstr "Las siguientes imágenes no están en el formato correcto (jpg/jpeg)."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid ""
"The following images are not in the correct format (jpg/jpeg).\n"
"\n"
"%(images)s"
msgstr ""
"Las siguientes imágenes no están en el formato correcto (jpg/jpeg).\n"
"\n"
"%(images)s"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "The following images do not meet the"
msgstr "Las siguientes imágenes no cumplen con la"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid ""
"The following images do not meet the required aspect ratio (between 1.91:1 and 4:5).\n"
"\n"
"%(images)s"
msgstr ""
"Las siguientes imágenes no cumplen con la relación de aspecto necesaria (entre 1.91:1 y 4:5).\n"
"\n"
"%(images)s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "There was a authentication issue during your request."
msgstr "Ocurrió un problema de autenticación durante su solicitud."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr "No está autorizado. Póngase en contacto con su administrador."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "Use su propia cuenta de Instragram"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr ""
"Se usa para permitir el acceso a Instagram para obtener la imagen de la "
"publicación"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Se utiliza para hacer comparaciones cuando necesitamos restringir algunas "
"funciones para una red social específica (\"Facebook\", \"X\", entre otras)."

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_image_ids
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_image_ids
msgid "Will attach images to your posts."
msgstr "Adjuntará las imágenes a sus publicaciones"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "You can only post up to 10 images at once."
msgstr "Solo puede publicar hasta 10 imágenes a la vez."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "No cuenta con una suscripción activa. Compre una aquí: %s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid ""
"You need to link your Instagram page to your Facebook account to post with Odoo Social.\n"
" Please create one and make sure it is linked to your account."
msgstr ""
"Necesita vincular su página de Instagram a su cuenta de Facebook para publicar con Marketing social de Odoo.\n"
"Cree una y asegúrese de que está vinculada a su cuenta."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "Your image appears to be corrupted, please try loading it again."
msgstr "Parece que su imagen está dañada, intente cargarla de nuevo."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "required aspect ratio"
msgstr "relación de aspecto necesaria"
