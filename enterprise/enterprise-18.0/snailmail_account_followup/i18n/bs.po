# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * snailmail_account_reports_followup
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"The letter will be sent using the IAP service from Odoo.&#10;Make sure you have enough credits on your account or proceed to a recharge.\"/>\n"
"                        <br/>"
msgstr ""

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "By post"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_res_partner
msgid "Contact"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Kriterijum praćenja"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Pošalji pismo"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "Sending this document by post will cost"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail
msgid "Snailmail"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail_cost
msgid "Stamps"
msgstr ""

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr ""

#. module: snailmail_account_followup
#. odoo-python
#: code:addons/snailmail_account_followup/models/account_followup_report.py:0
msgid "You are trying to send a letter by post, but no follow-up contact has any address set"
msgstr ""
