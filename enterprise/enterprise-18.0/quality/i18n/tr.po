# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality
# 
# Translators:
# emre oktem, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> YAYLA, 2024
# <PERSON><PERSON><PERSON> <tug<PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON> GÜNDOĞDU <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Melih Melik Sonmez, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Gökhan <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# İlknur Gözütok, 2024
# Halil, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Umur Akın <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alert_count
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_count
msgid "# Quality Alerts"
msgstr "# Kalite Uyarıları"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__check_count
msgid "# Quality Checks"
msgstr "Kalite Kontroller"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Bir Python sözlüğü, bu rumuz için yeni kayıtlar oluştururken varsayılan "
"değerleri sağlamak için değerlendirilir."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction
msgid "Action Needed"
msgstr "Aksiyon Gerekiyor"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_2
msgid "Action Proposed"
msgstr "Önerilen Eylem"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__active
msgid "Active"
msgstr "Etkin"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Etkinlik İstisna Dekorasyonu"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_state
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivite Türü İmgesi"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_check_view_activity
msgid "Activity view"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__additional_note
msgid "Additional Note"
msgstr "Ek Not"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_check__additional_note
msgid "Additional remarks concerning this check."
msgstr "Bu denetime ilişkin ek açıklamalar."

#. module: quality
#: model:res.groups,name:quality.group_quality_manager
msgid "Administrator"
msgstr "Yönetici"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__done
msgid "Alert Processed"
msgstr "İşlenen Uyarı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__alert_ids
msgid "Alerts"
msgstr "İkazlar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_id
msgid "Alias"
msgstr "Rumuz"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_contact
msgid "Alias Contact Security"
msgstr "Rumuz Kontak Güvenliği"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain_id
msgid "Alias Domain"
msgstr "Alias Domain"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_domain
msgid "Alias Domain Name"
msgstr "Takma Alan Adı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_full_name
msgid "Alias Email"
msgstr "Rumuz E-Posta"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_name
msgid "Alias Name"
msgstr "Rumuz Adı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_status
msgid "Alias Status"
msgstr "Takma Ad Durumu"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Alınan son mesajda değerlendirilen takma ad durumu."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_model_id
msgid "Aliased Model"
msgstr "Rumuzlanan Model"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Archived"
msgstr "Arşivlendi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_check__message_attachment_count
#: model:ir.model.fields,field_description:quality.field_quality_point__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__reason
msgid "Cause"
msgstr "Neden"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__check_id
#: model:ir.model.fields,field_description:quality.field_quality_point__check_ids
msgid "Check"
msgstr "Kontrol Et"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__check_count
msgid "Check Count"
msgstr "Kontrol Toplamı"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Close"
msgstr "Kapat"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__color
msgid "Color"
msgstr "Renk"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__color
msgid "Color Index"
msgstr "Renk"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__company_id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__company_id
#: model:ir.model.fields,field_description:quality.field_quality_check__company_id
#: model:ir.model.fields,field_description:quality.field_quality_point__company_id
msgid "Company"
msgstr "Firma"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_1
msgid "Confirmed"
msgstr "Doğrulanmış"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__control_date
msgid "Control Date"
msgstr "Kontrol Tarihi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__point_id
msgid "Control Point"
msgstr "Kontrol Noktası"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_corrective
msgid "Corrective Action"
msgstr "Düzeltici Faaliyet"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__create_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__create_date
#: model:ir.model.fields,field_description:quality.field_quality_check__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point__create_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__create_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__create_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Creation Date"
msgstr "Oluşturulma Tarihi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Özel İletilmeyen Mesajı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_assign
msgid "Date Assigned"
msgstr "Atanan Tarih"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__date_close
msgid "Date Closed"
msgstr "Kapanış Tarihi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_defaults
msgid "Default Values"
msgstr "Öntanımlı Değerler"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr "Kalite kontrol tipini tanımlar."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe the quality check to do..."
msgstr "Yapılması gereken kalite kontrolünü açıklayın..."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Describe why you need to perform this quality check..."
msgstr "Bu kalite kontrolünü neden gerçekleştirmeniz gerektiğini açıklayın..."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__description
msgid "Description"
msgstr "Açıklama"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__display_name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__display_name
#: model:ir.model.fields,field_description:quality.field_quality_check__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point__display_name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__display_name
#: model:ir.model.fields,field_description:quality.field_quality_reason__display_name
#: model:ir.model.fields,field_description:quality.field_quality_tag__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Done"
msgstr "Yapıldı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_email
msgid "Email Alias"
msgstr "E-Posta Rumuzu"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__email_cc
msgid "Email cc"
msgstr "E-posta cc"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"E-posta alan adı, örneğin '<EMAIL>' adresindeki 'example.com'"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__fail
msgid "Failed"
msgstr "Başarısız"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__folded
msgid "Folded"
msgstr "Katlanmış"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_follower_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_partner_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_type_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Yazı tipi harika simgesi ör. fa-görevler"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Group By"
msgstr "Grupla"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__has_message
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__has_message
#: model:ir.model.fields,field_description:quality.field_quality_check__has_message
#: model:ir.model.fields,field_description:quality.field_quality_point__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__2
msgid "High"
msgstr "Yüksek"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__id
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__id
#: model:ir.model.fields,field_description:quality.field_quality_check__id
#: model:ir.model.fields,field_description:quality.field_quality_point__id
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__id
#: model:ir.model.fields,field_description:quality.field_quality_reason__id
#: model:ir.model.fields,field_description:quality.field_quality_tag__id
msgid "ID"
msgstr "ID"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Rumuzu tutan üst kayıt ID (örnek: görev oluşturma rumuzunu tutan proje)"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_exception_icon
msgid "Icon"
msgstr "Simge"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_icon
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisnai bir etkinliği belirtmek için kullanılan simge."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error
#: model:ir.model.fields,help:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error
#: model:ir.model.fields,help:quality.field_quality_point__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Eğer belirlenirse, standart mesaj yerine yetkili olmayan kullanıcılara bu "
"içerik gönderilecektir."

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "In Progress"
msgstr "İşlemde"

#. module: quality
#: model:quality.point.test_type,name:quality.test_type_instructions
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Instructions"
msgstr "Açıklamalar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_check__message_is_follower
#: model:ir.model.fields,field_description:quality.field_quality_point__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_check__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_uid
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__write_date
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__write_date
#: model:ir.model.fields,field_description:quality.field_quality_check__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point__write_date
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__write_date
#: model:ir.model.fields,field_description:quality.field_quality_reason__write_date
#: model:ir.model.fields,field_description:quality.field_quality_tag__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Late Activities"
msgstr "Geciken Aktiviteler"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__lot_id
msgid "Lot"
msgstr "Lot"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__lot_id
msgid "Lot/Serial"
msgstr "Lot/Seri"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__1
msgid "Low"
msgstr "Düşük"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__my_activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Aktivite Son Tarihim"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "My Alerts"
msgstr "Kalite Uyarılarım"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__name
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__name
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__name
#: model:ir.model.fields,field_description:quality.field_quality_reason__name
msgid "Name"
msgstr "Adı"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
#: model:quality.alert.stage,name:quality.quality_alert_stage_0
msgid "New"
msgstr "Yeni"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_calendar_event_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sonraki Aktivite Takvimi Etkinliği"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_date_deadline
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Son Tarihi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_summary
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_type_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivitie Türü"

#. module: quality
#. odoo-python
#: code:addons/quality/models/quality.py:0
msgid ""
"No quality team found for this company.\n"
"Please go to configuration and create one first."
msgstr ""
"Bu şirket için nitelik takımı bulunamadı.\n"
"Lütfen yapılandırmaya gidin ve önce bir tane oluşturun."

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__0
msgid "Normal"
msgstr "Normal"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__note
#: model:ir.model.fields,field_description:quality.field_quality_point__note
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Note"
msgstr "Not"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
msgid "Notes"
msgstr "Notlar"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_needaction_counter
msgid "Number of Actions"
msgstr "Aksiyon Sayısı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_needaction_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "İşlem gerektiren mesaj sayısı"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_alert_team__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_check__message_has_error_counter
#: model:ir.model.fields,help:quality.field_quality_point__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__picking_type_ids
msgid "Operation Types"
msgstr "Operasyon Türü"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Operations"
msgstr "Operasyonlar"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Yanıt vermemiş olsalar bile, gelen tüm mesajların ekleneceği bir ileti "
"dizisinin (kayıt) isteğe bağlı kimliği. Ayarlanırsa, bu, yeni kayıtların "
"oluşturulmasını tamamen devre dışı bırakacaktır."

#. module: quality
#: model:quality.reason,name:quality.reason_other
msgid "Others"
msgstr "Diğerleri"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Üst Model"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Üst kayıt iş parçacığı kimliği"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Rumuzun bulunduğu üst model. Rumuz referansın bulunduğu model zorunlu olarak"
" alias_model_id tarafından verilen model değildir.  (örnek: project "
"(parent_model) ve görev (model))"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__partner_id
msgid "Partner"
msgstr "İş Ortağı"

#. module: quality
#: model:quality.reason,name:quality.reason_parts
msgid "Parts Quality"
msgstr "Parça Kalitesi"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__pass
msgid "Passed"
msgstr "Başarılı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__picking_id
#: model:ir.model.fields,field_description:quality.field_quality_check__picking_id
msgid "Picking"
msgstr "Toplama"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__picture
msgid "Picture"
msgstr "Resim"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Posta geçidini kullanarak gönderilecek mesajların kuralları:\n"
"- herkes: herkes posta gönderebilir\n"
"- iş ortakları: sadece onaylanmış iş ortakları\n"
"- takipçiler: sadece ilgili dokümanın veya takipçi kanalların takipçileri\n"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__action_preventive
msgid "Preventive Action"
msgstr "Önleyici Faaliyet"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Preview"
msgstr "Önizle"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__priority
msgid "Priority"
msgstr "Öncelik"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_tmpl_id
#: model:ir.model.fields,field_description:quality.field_quality_check__product_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Product"
msgstr "Ürün"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_category_ids
msgid "Product Categories"
msgstr "Ürün Kategorileri"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__product_id
msgid "Product Variant"
msgstr "Ürün Varyantı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point__product_ids
msgid "Products"
msgstr "Ürünler"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Alert"
msgstr "Kalite Alarmı"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_stage
msgid "Quality Alert Stage"
msgstr "Kalite Uyarı Aşaması"

#. module: quality
#: model:ir.model,name:quality.model_quality_alert_team
msgid "Quality Alert Team"
msgstr "Kalite Ekipleri"

#. module: quality
#: model:ir.model,name:quality.model_quality_check
msgid "Quality Check"
msgstr "Kalite Kontrol"

#. module: quality
#: model:ir.model,name:quality.model_quality_point
msgid "Quality Control Point"
msgstr "Kalite Kontrol Noktası"

#. module: quality
#: model:ir.model,name:quality.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr "Kalite Kontrol Test Türü"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_category_ids
msgid ""
"Quality Point will apply to every Products in the selected Product "
"Categories."
msgstr ""

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr "Kalite Noktası tüm seçili ürünlere uygulanacak."

#. module: quality
#: model:ir.model,name:quality.model_quality_tag
msgid "Quality Tag"
msgstr "Kalite Etiketi"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Quality Team"
msgstr "Nitelik Takımı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__rating_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__rating_ids
msgid "Ratings"
msgstr "Değerlendirmeler"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Kayıt Başlık ID"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__name
#: model:ir.model.fields,field_description:quality.field_quality_point__name
msgid "Reference"
msgstr "Referans"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__user_id
#: model:ir.model.fields,field_description:quality.field_quality_point__user_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Responsible"
msgstr "Sorumlu"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__activity_user_id
#: model:ir.model.fields,field_description:quality.field_quality_check__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__reason_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Root Cause"
msgstr "Kök Neden"

#. module: quality
#: model:ir.model,name:quality.model_quality_reason
msgid "Root Cause for Quality Failure"
msgstr "Nitelik Başarısızlığı için Temel Neden"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_check__message_has_sms_error
#: model:ir.model.fields,field_description:quality.field_quality_point__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS İleti hatası"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__sequence
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__sequence
#: model:ir.model.fields,field_description:quality.field_quality_point__sequence
msgid "Sequence"
msgstr "Sıralama"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: quality
#: model:quality.alert.stage,name:quality.quality_alert_stage_3
msgid "Solved"
msgstr "Çözüldü"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__stage_id
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Stage"
msgstr "Aşama"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__quality_state
msgid "Status"
msgstr "Durumu"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_state
#: model:ir.model.fields,help:quality.field_quality_check__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Aktivitelere göre durum \n"
"Gecikmiş\\: Son tarih geçmiş\n"
"Bugün\\: Aktivite tarihi bugün\n"
"Planlanmış\\: Gelecek Aktiviteler."

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_tag__name
msgid "Tag Name"
msgstr "Etiket Adı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__tag_ids
msgid "Tags"
msgstr "Etiketler"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
#: model:quality.point.test_type,name:quality.test_type_picture
msgid "Take a Picture"
msgstr "Bir Fotoğraf Çekin"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__team_id
#: model:ir.model.fields,field_description:quality.field_quality_check__team_id
#: model:ir.model.fields,field_description:quality.field_quality_point__team_id
msgid "Team"
msgstr "Ekip"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert_stage__team_ids
msgid "Teams"
msgstr "Ekipler"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__technical_name
msgid "Technical name"
msgstr "Teknik adı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__test_type_id
#: model:ir.model.fields,field_description:quality.field_quality_point__test_type_id
msgid "Test Type"
msgstr "Test Türü"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Bu rumuzun karşılık geldiği model (Odoo Belge Türü). Mevcut bir kayda yanıt "
"vermeyen herhangi bir gelen e-posta, bu modelin yeni bir kaydının "
"oluşturulmasına neden olacaktır (örneğin, bir Proje Görevi)"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Eposta rumuzunun adı, ör. <<EMAIL>> adresine gelen "
"epostaları yakalamak için 'kariyer'"

#. module: quality
#: model:res.groups,comment:quality.group_quality_manager
msgid "The quality manager manages the quality process"
msgstr "Kalite sorumlusu kalite sürecini yönetir"

#. module: quality
#: model:res.groups,comment:quality.group_quality_user
msgid "The quality user uses the quality process"
msgstr "Kaliteli kullanıcısı, kalite süreçlerini kullanır"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_check__title
#: model:ir.model.fields,field_description:quality.field_quality_point__title
msgid "Title"
msgstr "Başlık"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_check__quality_state__none
msgid "To do"
msgstr "Yapılacak"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_alert_view_search
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: quality
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_form
#: model_terms:ir.ui.view,arch_db:quality.quality_point_view_tree
msgid "Type"
msgstr "Tip"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__activity_exception_decoration
#: model:ir.model.fields,help:quality.field_quality_check__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıtlardaki istisna etkinliğinin türü."

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_tag__color
msgid "Used in the kanban view"
msgstr "Kanban görünümünde kullanılır"

#. module: quality
#: model:res.groups,name:quality.group_quality_user
msgid "User"
msgstr "Kullanıcı"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__partner_id
msgid "Vendor"
msgstr "Tedarikçi"

#. module: quality
#: model:ir.model.fields.selection,name:quality.selection__quality_alert__priority__3
msgid "Very High"
msgstr "Çok Yüksek"

#. module: quality
#. odoo-javascript
#: code:addons/quality/static/src/tablet_image_field/tablet_image_field.xml:0
msgid "Viewer"
msgstr "Görüntüleyici"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,field_description:quality.field_quality_point__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: quality
#: model:ir.model.fields,help:quality.field_quality_alert__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_alert_team__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_check__website_message_ids
#: model:ir.model.fields,help:quality.field_quality_point__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: quality
#: model:quality.reason,name:quality.reason_wo
msgid "Work Operation"
msgstr "Çalışma "

#. module: quality
#: model:quality.reason,name:quality.reason_workcenter
msgid "Workcenter Failure"
msgstr "İş Merkezi Arızası"

#. module: quality
#: model:ir.model.fields,field_description:quality.field_quality_point_test_type__active
msgid "active"
msgstr "etkin"
