# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_account
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.2alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-26 09:19+0000\n"
"PO-Revision-Date: 2025-02-07 17:07+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_l10n_be_274_xx
msgid "274.XX Sheets"
msgstr "Déclarations 274.XX"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de plan comptable"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_l10n_be_274_xx__move_id
msgid "Accounting Entry"
msgstr "Entrée comptable"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid "Accounting Entry Generated"
msgstr "Entrées comptables générées"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Adjustment Entry"
msgstr "Entrée d'ajustement"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_bachelor_account_id
msgid "Bachelors"
msgstr "Bacheliers"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_bachelor_capping_account_id
msgid "Bachelors Capping"
msgstr "Ecrêtement des bacheliers"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_doctor_master_account_id
msgid "Doctors/Civil Engineers/Masters"
msgstr "Docteurs/Ingénieurs Civil/Masters"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_bachelor_account_id
msgid "Exemption Bachelor Account"
msgstr "Compte d'exonération des bacheliers"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_bachelor_capping_account_id
msgid "Exemption Bachelor Capping Account"
msgstr "Compte d'écrêtement des bacheliers"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption Capping for bachelors"
msgstr "Ecrêtement des bacheliers"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_doctor_master_account_id
msgid "Exemption Doctor Master Account"
msgstr "Compte d'exonération des docteurs/masters"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption for bachelors"
msgstr "Exonération des bacheliers"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption for doctors/civil engineers/masters"
msgstr "Exonération des docteurs/ingénieurs civil/masters"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.res_config_settings_view_form
msgid "Journal Entries Accounts"
msgstr "Comptes des pièces comptables"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Please define a default journal on the exmption journal!"
msgstr "Veuillez définir un journal par défaut sur le journal d'exonération !"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid ""
"Please make sure that the journal and the accounts are correctly configured "
"on the Payroll Settings."
msgstr ""
"Veuillez vous assurer que le journal et les comptes sont correctement "
"configurés dans les paramètres de paie."

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid "Post Journal Entries"
msgstr "Comptabiliser les pièces comptables"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_journal_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_journal_id
msgid "Salary Journal"
msgstr "Journal des salaires"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid ""
"The withholing taxes exemption has been posted on the accounting entries:"
msgstr ""
"L'exonération du précompte professionnel a bien été enregistrée sur les "
"entrées comptables :"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.res_config_settings_view_form
msgid "Withholding Taxes Exemption"
msgstr "Exonération du précompte professionnel"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Withholding Taxes Exemption for %s"
msgstr "Exonération du précompte professionnel pour %s"
