<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mrp_workorder.QualityCheck">
        <li class="o_mrp_record_line list-group-item d-flex justify-content-between align-items-center"
            t-att-class="cssClass"
            t-on-click="onClick"
            t-on-animationend="onAnimationEnd">
            <span class="text-truncate" t-att-class="isComplete ? 'text-decoration-line-through opacity-25' : ''" t-esc="name"/>
            <span t-if="props.record.data.duration" t-att-class="active ? '' : 'text-muted'">
                <MrpTimer value="props.record.data.duration" ongoing="active"/>
            </span>
            <t t-if="props.quantityToProduce">
                <t t-if="props.record.data.product_tracking !== 'none'">
                    <button t-if="!props.record.data.lot_id" class="btn btn-info fa fa-plus" t-on-click.stop="pass"/>
                    <span t-else="" class="o_qc_lot" t-out="props.record.data.lot_id[1]" t-on-click.stop="pass"/>
                </t>
                <button t-else="" t-on-click.stop="pass" class="btn btn-sm text-nowrap" t-att-class="isComplete ? 'btn-outline-secondary disabled' : 'btn-secondary'">
                    <t t-if="props.record.data.qty_done &amp;&amp; props.record.data.qty_done != props.quantityToProduce">
                        <t t-esc="props.record.data.qty_done"/> /
                    </t>
                    <t t-esc="props.quantityToProduce"/>
                    <span class="ms-1" t-esc="uom"/>
                </button>
            </t>
            <button t-else="" class="btn btn-lg px-0" t-on-click.stop="pass"
                    t-attf-class="#{props.record.data.quality_state == 'none' and icon}"
                    t-att-class="{
                    'pass fa fa-check text-success' : shouldDisplayCheckmark,
                    'fail fa fa-times text-danger' : failed}">
                <span t-if="showMeasure" t-out="props.record.data.measure + ' ' + props.record.data.norm_unit"/>
                <span t-elif="lotInfo" t-out="lotInfo"/>
            </button>
        </li>
    </t>
</templates>