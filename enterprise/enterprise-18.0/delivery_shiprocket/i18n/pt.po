# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_shiprocket
# 
# Translators:
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# NumerSpiral HBG, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>Click on Add a New Channel</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Copy your API User's Email and Password and paste into Shipping method's "
"configuration.</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Fill the details and Click on Save Channel &amp; Test Connection "
"button.</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>In Odoo configuration click on <i class=\"fa fa-refresh oe_inline\"/> "
"icon near Shiprocket Channel to sync newly created channels.</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>Now click on Add for MANUAL</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Once your account is created, go to Settings &gt; API &gt; Configure. </b>\n"
"                                <b>You can add new API User from the right side of the page.</b>\n"
"                                <br/>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>To Create a channel, Go to Shiprocket Channels</b>"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In test environment, to avoid "
"charges, your shippings are automatically <b>cancelled</b> after the label "
"creation. The amount of cancelled shipment will be returned in next 3-4 "
"days."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_manifests_generate
msgid ""
"A manifest is a document that is required by some carriers to streamline the"
" pickup process.particularly when shipping out a high-volume of ecommerce "
"orders."
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "API User"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "AWB assignment was unsuccessful: %s"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "AWB number(s) not found to cancel the shipment!"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Access token is generated successfully!"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Add Manual Channel"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Add New Channel"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Authentication failed! Please check your credentials."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__shiprocket_payment_method__cod
msgid "COD"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Transportadora"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__channel_code
msgid "Channel Code"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__name
msgid "Channel Name"
msgstr "Nome do Canal"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Configure Shiprocket channel in shipping method"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Country is required!"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier (%(recommender)s): %(courier)s"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__courier_code
msgid "Courier Code"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier is not available for delivery!"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier: %s"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Create API User"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_pickup_request
msgid ""
"Create a pickup request for your order shipment using Validate button of the"
" Delivery Order."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__create_uid
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__create_date
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__create_date
msgid "Created on"
msgstr "Criado em"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__display_name
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_picking__eway_bill_number
msgid "EWay Bill"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Email is required!"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_password
msgid "Enter your Password from Shiprocket account (API)."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_email
msgid "Enter your Username from Shiprocket account (API)."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid ""
"Eway Bill number is required to ship an order if order amount is more than "
"50,000 INR."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Failed to fetch Shiprocket Channel(s), Please try again later."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Failed to fetch Shiprocket Couriers(s), Please try again later."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_manifests_generate
msgid "Generate Manifest"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_access_token
msgid "Generate access token using Shiprocket credentials"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_courier_ids
msgid ""
"Get all the integrated Couriers from your Shiprocket account.Based on the "
"courier selections the rate will be fetched from the Shiprocket."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_channel_id
msgid ""
"Get all the integrated channels from your Shiprocket account.This channel id"
" is used to select or specify a custom channel at the time of Shiprocket "
"order creation."
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Go to"
msgstr "Ir para"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Go to Shipping Method"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Go to Shipping Methods"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__id
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__id
msgid "ID"
msgstr "Id."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Label generated for %(courier)s with Tracking Number: %(tracking_ref)s"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__write_uid
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__write_date
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/stock_package_type.py:0
msgid "Length, Width and Height is necessary for Shiprocket Package."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Manifest generated of %s"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Order cancelled successfully!"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_default_package_type_id
msgid "Package Type"
msgstr "Tipo de Embalagem"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_payment_method
msgid "Payment Method"
msgstr "Método de Pagamento"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Phone or Mobile is required!"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_pickup_request
msgid "Pickup Request"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Pincode is required!"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Please configure Shiprocket credentials in the shipping method"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__shiprocket_payment_method__prepaid
msgid "Prepaid"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Provedor"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "SKU is missing!"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Sale Order or Picking is required to get rate."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid ""
"Same order is available in Shiprocket so label provided is the copy of "
"existing one."
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Save Manual Channel"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__name
msgid "Service Name"
msgstr ""

#. module: delivery_shiprocket
#: model:product.template,name:delivery_shiprocket.product_product_delivery_shiprocket_product_template
msgid "ShipRocket"
msgstr ""

#. module: delivery_shiprocket
#: model:delivery.carrier,name:delivery_shiprocket.delivery_carrier_shiprocket
msgid "ShipRocket Domestic"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Métodos de Envio"

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__delivery_type__shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__stock_package_type__package_carrier_type__shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.stock_picking_form_inherit_shiprocket
msgid "Shiprocket"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_access_token
msgid "Shiprocket Access Token"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_shiprocket_channel
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_channel_id
msgid "Shiprocket Channel"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Configuration"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_shiprocket_courier
msgid "Shiprocket Courier"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_courier_ids
msgid "Shiprocket Couriers"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_email
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__shiprocket_email
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__shiprocket_email
msgid "Shiprocket Email"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Shiprocket Error: %s"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Shiprocket Notification"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_picking__shiprocket_orders
msgid "Shiprocket Order(s)"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_password
msgid "Shiprocket Password"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.res_config_settings_view_form_sale_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.res_config_settings_view_form_stock_shiprocket
msgid "Shiprocket Shipping Methods"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Tutorial"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Tutorial for add channel"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Website"
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Shiprocket order(s) not found to cancel the shipment!"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_default_package_type_id
msgid ""
"Shiprocket requires package dimensions for getting accurate rate, you can "
"define these in a package type that you set as default"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_token_valid_upto
msgid ""
"Shiprocket token expires in 10 days. Token will be auto generate based on "
"this token expiry date."
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sign up"
msgstr "Registar"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_stock_picking__shiprocket_orders
msgid ""
"Store shiprocket order(s) in a (+) separated string, used in cancelling the "
"order."
msgstr ""

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Street is required!"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sync Channel from Shiprocket"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sync Couriers from Shiprocket"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Test Connection"
msgstr "Testar Ligação"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_payment_method
msgid ""
"The method of payment. Can be either COD (Cash on delivery) Or Prepaid while"
" creating Shiprocket order."
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_token_valid_upto
msgid "Token Expiry"
msgstr ""

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_stock_picking
msgid "Transfer"
msgstr "Transferência"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Weight is missing!"
msgstr ""

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "to create a new account:"
msgstr ""
