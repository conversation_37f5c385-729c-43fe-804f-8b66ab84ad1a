# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_shiprocket
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON><PERSON> <mikko.nar<PERSON><PERSON>@web-veistamo.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>Click on Add a New Channel</b>"
msgstr "<b>Napsauta Lisää uusi kanava</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Copy your API User's Email and Password and paste into Shipping method's "
"configuration.</b>"
msgstr ""
"<b>Kopioi API-käyttäjän sähköpostiosoite ja salasana ja liitä ne "
"toimitusmenetelmän asetuksiin.</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Fill the details and Click on Save Channel &amp; Test Connection "
"button.</b>"
msgstr ""
"<b>Täytä tiedot ja napsauta Tallenna kanava ja Testaa yhteyttä "
"-painiketta.</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>In Odoo configuration click on <i class=\"fa fa-refresh oe_inline\"/> "
"icon near Shiprocket Channel to sync newly created channels.</b>"
msgstr ""
"<b>Napsauta Odoon asetuksissa <i class=\"fa fa-refresh oe_inline\"/> "
"-kuvaketta Shiprocket-kanavan vieressä synkronoidaksesi äskettäin luodut "
"kanavat.</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>Now click on Add for MANUAL</b>"
msgstr "<b>Klikkaa nyt Add for MANUAL</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<b>Once your account is created, go to Settings &gt; API &gt; Configure. </b>\n"
"                                <b>You can add new API User from the right side of the page.</b>\n"
"                                <br/>"
msgstr ""
"<b>Kun tilisi on luotu, siirry kohtaan Asetukset &gt; API &gt; Määritä. </b>\n"
"                                <b>Voit lisätä uuden API-käyttäjän sivun oikeasta reunasta.</b>\n"
"                                <br/>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "<b>To Create a channel, Go to Shiprocket Channels</b>"
msgstr "<b>Luodaksesi kanavan siirry Shiprocket-kanaviin</b>"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In test environment, to avoid "
"charges, your shippings are automatically <b>cancelled</b> after the label "
"creation. The amount of cancelled shipment will be returned in next 3-4 "
"days."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Testiympäristössä lähetys "
"<b>peruutetaan</b> automaattisesti etiketin luomisen jälkeen, jotta "
"vältyttäisiin veloituksilta. Peruutettujen lähetysten määrä palautetaan "
"seuraavien 3-4 päivän aikana."

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_manifests_generate
msgid ""
"A manifest is a document that is required by some carriers to streamline the"
" pickup process.particularly when shipping out a high-volume of ecommerce "
"orders."
msgstr ""
"Kuormakirja (manifesti) on asiakirja, jota jotkut kuljetusliikkeet vaativat "
"noutoprosessin tehostamiseksi. Erityisesti silloin, kun lähetät suuria "
"määriä verkkokauppatilauksia."

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "API User"
msgstr "API-käyttäjä"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "AWB assignment was unsuccessful: %s"
msgstr "AWB:n osoittaminen ei onnistunut: %s"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "AWB number(s) not found to cancel the shipment!"
msgstr "AWB-numeroa (-numeroita) ei löydy lähetyksen peruuttamiseksi!"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Access token is generated successfully!"
msgstr "Pääsytunniste on luotu onnistuneesti!"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Add Manual Channel"
msgstr "Lisää manuaalinen kanava"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Add New Channel"
msgstr "Lisää uusi kanava"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Authentication failed! Please check your credentials."
msgstr "Tunnistautuminen epäonnistui! Tarkista tunnukset."

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__shiprocket_payment_method__cod
msgid "COD"
msgstr "COD"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Huolitsija"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__channel_code
msgid "Channel Code"
msgstr "Kanavakoodi"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__name
msgid "Channel Name"
msgstr "Kanavan nimi"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Configure Shiprocket channel in shipping method"
msgstr "Määritä Shiprocket-kanava lähetysmenetelmään"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Country is required!"
msgstr "Maa on pakollinen!"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier (%(recommender)s): %(courier)s"
msgstr "Kuriiri (%(recommender)s): %(courier)s"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__courier_code
msgid "Courier Code"
msgstr "Kuriirikoodi"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier is not available for delivery!"
msgstr "Kuriiri ei ole käytettävissä toimitukseen!"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Courier: %s"
msgstr "Kuriiri: %s"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Create API User"
msgstr "Luo API-käyttäjä"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_pickup_request
msgid ""
"Create a pickup request for your order shipment using Validate button of the"
" Delivery Order."
msgstr ""
"Luo noutopyyntö tilauslähetystäsi varten käyttämällä Toimitustilauksen "
"Vahvista-painiketta."

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__create_uid
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__create_date
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__create_date
msgid "Created on"
msgstr "Luotu"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__display_name
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_picking__eway_bill_number
msgid "EWay Bill"
msgstr "Sähköinen kuormakirja"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Email is required!"
msgstr "Sähköposti vaaditaan!"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_password
msgid "Enter your Password from Shiprocket account (API)."
msgstr "Syötä salasanasi Shiprocket-tililtäsi (API)."

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_email
msgid "Enter your Username from Shiprocket account (API)."
msgstr "Syötä käyttäjätunnuksesi Shiprocket-tililtäsi (API)."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid ""
"Eway Bill number is required to ship an order if order amount is more than "
"50,000 INR."
msgstr ""
"Kuormakirjan numero tarvitaan tilauksen lähettämiseen, jos tilauksen summa "
"on yli 50 000 INR."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Failed to fetch Shiprocket Channel(s), Please try again later."
msgstr ""
"Shiprocket-kanavan (-kanavien) haku epäonnistui, yritä myöhemmin uudelleen."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Failed to fetch Shiprocket Couriers(s), Please try again later."
msgstr "Shiprocket-kuriirien haku epäonnistui, yritä myöhemmin uudelleen."

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_manifests_generate
msgid "Generate Manifest"
msgstr "Luo kuormakirja"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_access_token
msgid "Generate access token using Shiprocket credentials"
msgstr "Luo käyttöoikeustunniste Shiprocketin tunnistetietojen avulla"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_courier_ids
msgid ""
"Get all the integrated Couriers from your Shiprocket account.Based on the "
"courier selections the rate will be fetched from the Shiprocket."
msgstr ""
"Hae kaikki integroidut kuriirit Shiprocket-tililtäsi. Kuriirivalintojen "
"perusteella hinta haetaan Shiprocketista."

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_channel_id
msgid ""
"Get all the integrated channels from your Shiprocket account.This channel id"
" is used to select or specify a custom channel at the time of Shiprocket "
"order creation."
msgstr ""
"Hae kaikki integroidut kanavat Shiprocket-tililtäsi. Kanavatunnusta "
"käytetään mukautetun kanavan valitsemiseen tai määrittämiseen Shiprocket-"
"tilauksen luomisen yhteydessä."

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Go to"
msgstr "Siirry"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Go to Shipping Method"
msgstr "Siirry toimitustapaan"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Go to Shipping Methods"
msgstr "Siirry toimitustapoihin"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__id
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__id
msgid "ID"
msgstr "ID"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Label generated for %(courier)s with Tracking Number: %(tracking_ref)s"
msgstr "Osoite-etiketti luotu %(courier)s ja seurantanumero: %(tracking_ref)s"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__write_uid
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__write_date
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/stock_package_type.py:0
msgid "Length, Width and Height is necessary for Shiprocket Package."
msgstr "Pituus, leveys ja korkeus vaaditaan Shiprocket-pakettia varten."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Manifest generated of %s"
msgstr "Kuormakirja luotu %s"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Order cancelled successfully!"
msgstr "Tilaus on peruutettu onnistuneesti!"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_default_package_type_id
msgid "Package Type"
msgstr "Pakkauksen tyyppi"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_payment_method
msgid "Payment Method"
msgstr "Maksutapa"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Phone or Mobile is required!"
msgstr "Puhelin tai matkapuhelin tarvitaan!"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_pickup_request
msgid "Pickup Request"
msgstr "Noutopyyntö"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Pincode is required!"
msgstr "PIN-koodi tarvitaan!"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Please configure Shiprocket credentials in the shipping method"
msgstr "Määritä Shiprocket-tunnukset lähetysmenetelmässä"

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__shiprocket_payment_method__prepaid
msgid "Prepaid"
msgstr "Ennalta maksettu"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Palveluntarjoaja"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "SKU is missing!"
msgstr "SKU puuttuu!"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Sale Order or Picking is required to get rate."
msgstr "Myyntitilaus tai poiminta vaaditaan hinnan saamiseksi."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid ""
"Same order is available in Shiprocket so label provided is the copy of "
"existing one."
msgstr ""
"Sama tilaus on saatavilla Shiprocketissa, joten toimitettu etiketti on kopio"
" olemassa olevasta etiketistä."

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Save Manual Channel"
msgstr "Tallenna manuaalinen kanava"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__name
msgid "Service Name"
msgstr "Palvelun nimi"

#. module: delivery_shiprocket
#: model:product.template,name:delivery_shiprocket.product_product_delivery_shiprocket_product_template
msgid "ShipRocket"
msgstr "ShipRocket"

#. module: delivery_shiprocket
#: model:delivery.carrier,name:delivery_shiprocket.delivery_carrier_shiprocket
msgid "ShipRocket Domestic"
msgstr "ShipRocket Kotimaa"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Toimitustavat"

#. module: delivery_shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__delivery_carrier__delivery_type__shiprocket
#: model:ir.model.fields.selection,name:delivery_shiprocket.selection__stock_package_type__package_carrier_type__shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.stock_picking_form_inherit_shiprocket
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_access_token
msgid "Shiprocket Access Token"
msgstr "Shiprocket pääsytunniste"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_shiprocket_channel
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_channel_id
msgid "Shiprocket Channel"
msgstr "Shiprocket-kanava"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Configuration"
msgstr "Shiprocket-asetukset"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket-liitin"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_shiprocket_courier
msgid "Shiprocket Courier"
msgstr "Shiprocket-kuriiri"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_courier_ids
msgid "Shiprocket Couriers"
msgstr "Shiprocket-kuriirit"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_email
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_channel__shiprocket_email
#: model:ir.model.fields,field_description:delivery_shiprocket.field_shiprocket_courier__shiprocket_email
msgid "Shiprocket Email"
msgstr "Shiprocket sähköpostiosoite"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Shiprocket Error: %s"
msgstr "Shiprocket virhe: %s"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Shiprocket Notification"
msgstr "Shiprocket-ilmoitus"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_stock_picking__shiprocket_orders
msgid "Shiprocket Order(s)"
msgstr "Shiprocket-tilaukset"

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_password
msgid "Shiprocket Password"
msgstr "Shiprocket salasana"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.res_config_settings_view_form_sale_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.res_config_settings_view_form_stock_shiprocket
msgid "Shiprocket Shipping Methods"
msgstr "Shiprocketin toimitustavat"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Tutorial"
msgstr "Shiprocket opetusohjelma"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Tutorial for add channel"
msgstr "Shiprocket Tutorial kanavan lisäämiseksi"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Shiprocket Website"
msgstr "Shiprocket verkkosivu"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/delivery_carrier.py:0
msgid "Shiprocket order(s) not found to cancel the shipment!"
msgstr "Shiprocket-tilausta ei löydy lähetyksen perumiseksi!"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_default_package_type_id
msgid ""
"Shiprocket requires package dimensions for getting accurate rate, you can "
"define these in a package type that you set as default"
msgstr ""
"Shiprocket vaatii paketin mitat tarkan hinnan saamiseksi. Voit määrittää ne "
"pakettityypissä, jonka oletusarvoksi asetat"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_token_valid_upto
msgid ""
"Shiprocket token expires in 10 days. Token will be auto generate based on "
"this token expiry date."
msgstr ""
"Shiprocket-pääsytunniste vanhenee 10 päivän kuluttua. Pääsytunniste luodaan "
"automaattisesti tämän tunnisteen voimassaolon päättymispäivän perusteella."

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sign up"
msgstr "Rekisteröidy"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_stock_package_type
msgid "Stock package type"
msgstr "Varastopaketin tyyppi"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_stock_picking__shiprocket_orders
msgid ""
"Store shiprocket order(s) in a (+) separated string, used in cancelling the "
"order."
msgstr ""
"Tallenna shiprocket-tilaus(t) (+)-merkillä erotettuna merkkijonona, jota "
"käytetään tilauksen peruuttamiseen."

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Street is required!"
msgstr "Katu on pakollinen!"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sync Channel from Shiprocket"
msgstr "Synkronoi kanava Shiprocketista"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Sync Couriers from Shiprocket"
msgstr "Synkronoi kuriirit Shiprocketista"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "Test Connection"
msgstr "Testaa yhteyttä"

#. module: delivery_shiprocket
#: model:ir.model.fields,help:delivery_shiprocket.field_delivery_carrier__shiprocket_payment_method
msgid ""
"The method of payment. Can be either COD (Cash on delivery) Or Prepaid while"
" creating Shiprocket order."
msgstr ""
"Maksutapa. Voi olla joko COD (postiennakko) tai Prepaid, kun luot Shiprocket"
" tilauksen."

#. module: delivery_shiprocket
#: model:ir.model.fields,field_description:delivery_shiprocket.field_delivery_carrier__shiprocket_token_valid_upto
msgid "Token Expiry"
msgstr "Pääsytunnisteen voimassaolon päättyminen"

#. module: delivery_shiprocket
#: model:ir.model,name:delivery_shiprocket.model_stock_picking
msgid "Transfer"
msgstr "Siirto"

#. module: delivery_shiprocket
#. odoo-python
#: code:addons/delivery_shiprocket/models/shiprocket_request.py:0
msgid "Weight is missing!"
msgstr "Paino puuttuu!"

#. module: delivery_shiprocket
#: model_terms:ir.ui.view,arch_db:delivery_shiprocket.view_delivery_carrier_form_inherit_delivery_shiprocket
msgid "to create a new account:"
msgstr "luodaksesi uuden tilin:"
