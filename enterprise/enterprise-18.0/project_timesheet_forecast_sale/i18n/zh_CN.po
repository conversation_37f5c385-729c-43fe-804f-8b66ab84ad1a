# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_forecast_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> CHEN <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_timesheet_forecast_sale
#. odoo-python
#: code:addons/project_timesheet_forecast_sale/models/project.py:0
msgid "%(name)s's Timesheets and Planning Analysis"
msgstr "%(name)s的工时表和计划分析"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_planning_analysis_report__billable_allocated_hours
msgid "Billable Time Allocated"
msgstr "已分配的计费时间"

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_consultant
msgid "Consultant"
msgstr "顾问"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_billable_hours
msgid "Effective Billable Time"
msgstr "实际计费时间"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_margin
msgid "Effective Margin"
msgstr "实际利润"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_non_billable_hours
msgid "Effective Non-Billable Time"
msgstr "实际不计费时间"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_revenues
msgid "Effective Revenues"
msgstr "实际收入"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_planning_analysis_report__non_billable_allocated_hours
msgid "Non-billable Time Allocated"
msgstr "已分配的不计费时间"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_billable_hours
msgid "Planned Billable Time"
msgstr "已安排的可计费时间"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_margin
msgid "Planned Margin"
msgstr "计划利润"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_non_billable_hours
msgid "Planned Non-Billable Time"
msgstr "已安排的不计费时间"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_revenues
msgid "Planned Revenues"
msgstr "计划收入"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "计划分析报表"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_planning_slot
msgid "Planning Shift"
msgstr "计划转变"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_project
msgid "Project"
msgstr "项目"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_update
msgid "Project Update"
msgstr "项目更新"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单明细"

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_junior_architect
msgid "Software Junior Architect"
msgstr "软件初级架构师"

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_senior_architect
msgid "Software Senior Architect"
msgstr "软件高级架构师"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,help:project_timesheet_forecast_sale.field_planning_analysis_report__billable_allocated_hours
msgid "Sum of hours allocated to shifts linked to a SOL."
msgstr "分配给与 SOL 有关的班次的小时数总和。"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,help:project_timesheet_forecast_sale.field_planning_analysis_report__non_billable_allocated_hours
msgid "Sum of hours allocated to shifts not linked to a SOL."
msgstr "分配给与 SOL 无关的班次的小时数总和。"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_timesheet_forecast_report_analysis
msgid "Timesheet & Planning Statistics"
msgstr "工时表和计划统计"

#. module: project_timesheet_forecast_sale
#. odoo-python
#: code:addons/project_timesheet_forecast_sale/models/project.py:0
msgid "Timesheets and Planning"
msgstr "工时表和计划"

#. module: project_timesheet_forecast_sale
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast_sale.project_timesheet_forecast_view_kanban_inherit_sale_timesheet
msgid "Timesheets and Planning Analysis"
msgstr "工时表和计划分析"
