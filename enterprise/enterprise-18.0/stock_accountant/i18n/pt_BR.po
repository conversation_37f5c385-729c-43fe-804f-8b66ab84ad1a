# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_accountant
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Automatic Accounting"
msgstr "Contabilidade automática"

#. module: stock_accountant
#: model:ir.model,name:stock_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Linha de extrato bancário"

#. module: stock_accountant
#: model:ir.model,name:stock_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Enable automatic accounting entries for stock movements"
msgstr ""
"Habilitar lançamentos contábeis automáticos para movimentações de estoque"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_account_expense_categ_id
msgid "Expense Account"
msgstr "Conta de despesas"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "General Account Properties"
msgstr "Propriedades gerais da conta"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_account_income_categ_id
msgid "Income Account"
msgstr "Conta de receitas"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Conta de entrada de estoque"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_journal
msgid "Stock Journal"
msgstr "Diário de estoque"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Conta de saída do estoque"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Stock Valuation"
msgstr "Valoração de estoque"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Conta de valoração de estoque"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid ""
"The below accounts will be used by default for automatic inventory "
"valuation."
msgstr ""
"As contas abaixo serão usadas por padrão para avaliação automática do "
"inventário."
