<?xml version="1.0" encoding="ISO-8859-1"?>
<odoo>
    <data>
        <template id="aec_template_yield_document">
        <DTECedido xmlns="http://www.sii.cl/SiiDte" version="1.0">
            <DocumentoDTECedido ID="DTE_Cedido">
        <t t-out="dte_file"/><TmstFirma t-out="get_cl_current_strftime()"/>
            </DocumentoDTECedido>
        </DTECedido>
        </template>

        <template id="aec_template_yield_contract">
        <Cesion version="1.0">
            <DocumentoCesion t-att-ID="'Odoo_Cesion_%s' % move.name.replace(' ', '_')">
                <SeqCesion t-out="sequence"/>
                <IdDTE>
                    <TipoDTE t-out="move.l10n_latam_document_type_id.code"/>
                    <RUTEmisor t-out="move.company_id.vat"/>
                    <RUTReceptor t-out="move.commercial_partner_id.vat"/>
                    <Folio t-out="int(move.l10n_latam_document_number)"/>
                    <FchEmis t-out="move.invoice_date"/>
                    <t t-set="decimals" t-value="0 if not move.l10n_latam_document_type_id._is_doc_type_export() else 2"/>
                    <MntTotal t-out="float_repr(move.amount_total, decimals)"/>
                </IdDTE>
                <Cedente>
                    <RUT t-out="move.company_id.vat"/>
                    <RazonSocial t-out="move.company_id.name"/>
                    <Direccion t-out="('%s, %s %s, %s' % (move.company_id.street or '', move.company_id.street2 or '', move.company_id.city or '', move.company_id.state_id.name or ''))[:80]"/>
                    <eMail t-out="move.company_id.l10n_cl_dte_email or move.company_id.email"/>
                    <RUTAutorizado>
                        <RUT t-out="signatory['vat']"/>
                        <Nombre t-out="signatory['name']"/>
                    </RUTAutorizado>
                    <DeclaracionJurada t-out="'Yo, %s, RUN %s, representando a %s, RUT %s, declaro que he puesto a disposición del cesionario %s, RUT %s, el documento donde constan los recibos de la recepción de mercaderías entregadas o servicios prestados, entregados por parte del deudor de la factura %s, RUT %s, de acuerdo a lo establecido en la Ley N° 19.983' % (signatory['name'], signatory['vat'], move.company_id.name, move.company_id.vat, assignee['name'], assignee['vat'], move.commercial_partner_id.name or move.partner_id.name, move.commercial_partner_id.vat or move.partner_id.vat)"/>
                </Cedente>
                <Cesionario>
                    <RUT t-out="assignee['vat']"/>
                    <RazonSocial t-out="assignee['name']"/>
                    <Direccion t-out="assignee['address'][:80]"/>
                    <eMail t-out="assignee['email']"/>
                </Cesionario>
                <MontoCesion t-out="float_repr(move.amount_total, decimals)"/>
                <UltimoVencimiento t-out="move.invoice_date_due"/>
                <TmstCesion t-out="get_cl_current_strftime()"/>
            </DocumentoCesion></Cesion>
        </template>

<template id="aec_template_yields">
    <DocumentoAEC ID="AEC">
        <Caratula version="1.0">
            <RutCedente t-out="company_id.vat"/>
            <RutCesionario t-out="assignee['vat']"/>
            <TmstFirmaEnvio t-out="get_cl_current_strftime()"/>
        </Caratula>
        <Cesiones>
            <t t-out="aec_document"/>
        </Cesiones>
    </DocumentoAEC>
</template>

<template id="aec_template">
<AEC xmlns="http://www.sii.cl/SiiDte" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sii.cl/SiiDte AEC_v10.xsd" version="1.0">
    <t t-out="signed_aec"/>
</AEC>
</template>

    </data>
</odoo>
