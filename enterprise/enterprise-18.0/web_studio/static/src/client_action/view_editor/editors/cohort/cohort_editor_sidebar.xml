<templates>

    <t t-name="web_studio.ViewEditor.CohortEditorSidebar">
        <InteractiveEditorSidebar>
            <t t-set-slot="view">
                <Property
                    name="'date_start'"
                    type="'selection'"
                    value="modelParams.dateStart"
                    childProps="{ choices: this.dateFields }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Start Date Field
                </Property>

                <Property
                    name="'date_stop'"
                    type="'selection'"
                    value="modelParams.dateStop"
                    childProps="{ choices: this.dateFields }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Stop Date Field
                </Property>

                <Property
                    name="'measure'"
                    type="'selection'"
                    value="modelParams.measure"
                    childProps="{ choices: this.dateFields, required: false }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Measure Field
                </Property>

                <Property
                    name="'interval'"
                    type="'selection'"
                    value="modelParams.interval"
                    childProps="{ choices: this.intervalChoices }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Interval
                </Property>

                <Property
                    name="'mode'"
                    type="'selection'"
                    value="modelParams.mode || 'retention'"
                    childProps="{ choices: this.modeChoices }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Mode
                </Property>

                <Property
                    name="'timeline'"
                    type="'selection'"
                    value="modelParams.timeline || 'forward'"
                    childProps="{ choices: this.timelineChoices }"
                    onChange.bind="onViewAttributeChanged"
                >
                    Timeline
                </Property>

                <SidebarViewToolbox onMore="props.openViewInForm" canEditXml="!viewEditorModel.isEditingSubview" openDefaultValues="props.openDefaultValues" />
            </t>
        </InteractiveEditorSidebar>
    </t>

</templates>
