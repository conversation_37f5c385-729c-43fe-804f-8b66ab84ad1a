# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_stock_inter_company_rules
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_company
msgid "Companies"
msgstr "会社"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: sale_purchase_stock_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_stock_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_stock_inter_company_rules/models/sale_order.py:0
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr "メニューから会社(%s)の正しい倉庫を設定する: 設定/ユーザー/会社"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid ""
"Default Operation type to set on Receipts that will be created for inter-"
"company transfers"
msgstr "会社間振替で作成される入荷に設定するデフォルトのオペレーションタイプ"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr "この会社への販売(購買)オーダに基づいて作成される購買(販売)オーダに設定されるデフォルト値。"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "購買オーダ"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid "Receipt Operation Type"
msgstr "入荷オペレーションタイプ"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_sync_delivery_receipt
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_sync_delivery_receipt
msgid "Synchronize Deliveries to your Receipts"
msgstr "配送を入荷に同期する"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_stock_picking
msgid "Transfer"
msgstr "転送"

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Operation"
msgstr "オペレーションを使用"

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr "倉庫を使用"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
msgid "Warehouse"
msgstr "倉庫"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "購買オーダ用倉庫"
